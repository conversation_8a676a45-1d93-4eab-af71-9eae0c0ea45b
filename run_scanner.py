#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源扫描器 - 简化运行脚本
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🔍 Android项目无用资源扫描器")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = Path.cwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查是否为Android项目
    if not (current_dir / "app" / "build.gradle").exists() and \
       not (current_dir / "app" / "build.gradle.kts").exists():
        print("\n❌ 当前目录不是有效的Android项目")
        print("请在Android项目根目录下运行此脚本")
        print("项目根目录应包含 app/build.gradle 或 app/build.gradle.kts 文件")
        return 1
    
    print("✅ 检测到Android项目")
    
    # 导入并运行扫描器
    try:
        from unused_resources_scanner import AndroidUnusedResourcesScanner
        
        print("\n🚀 开始扫描...")
        scanner = AndroidUnusedResourcesScanner(str(current_dir))
        scanner.scan_all()
        
        print("\n🎉 扫描完成！")
        print("\n📋 扫描结果:")
        print(f"   - 未使用的资源文件: {len(scanner.unused_resources)} 个")
        print(f"   - 未使用的类文件: {len(scanner.unused_classes)} 个")
        
        if scanner.unused_resources or scanner.unused_classes:
            total_size = sum(res['size'] for res in scanner.unused_resources) + \
                        sum(cls['size'] for cls in scanner.unused_classes)
            print(f"   - 可节省空间: {scanner._format_size(total_size)}")
            print("\n📄 详细报告已生成，请查看项目根目录下的报告文件")
        else:
            print("\n✨ 恭喜！没有发现未使用的文件")
        
        return 0
        
    except ImportError:
        print("❌ 无法导入扫描器模块，请确保 unused_resources_scanner.py 文件存在")
        return 1
    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 扫描过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
