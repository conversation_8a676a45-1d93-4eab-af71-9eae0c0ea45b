#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目智能扫描器
支持ViewBinding、DataBinding，带进度提示
"""

import os
import re
import time
import sys
from pathlib import Path
from collections import defaultdict

def print_progress(current, total, prefix="进度"):
    """打印进度条"""
    percent = (current / total) * 100
    bar_length = 30
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    sys.stdout.write(f'\r{prefix}: |{bar}| {current}/{total} ({percent:.1f}%)')
    sys.stdout.flush()

def layout_to_binding_class(layout_name):
    """将布局文件名转换为ViewBinding类名"""
    # activity_main -> ActivityMainBinding
    parts = layout_name.split('_')
    camel_case = ''.join(word.capitalize() for word in parts)
    return f"{camel_case}Binding"

def scan_android_project(project_root):
    """扫描Android项目"""
    project_path = Path(project_root)
    app_res = project_path / "app" / "src" / "main" / "res"
    app_java = project_path / "app" / "src" / "main" / "java"
    
    print(f"🔍 智能扫描项目: {project_path}")
    
    if not app_res.exists():
        print("❌ 找不到app/src/main/res目录")
        return
    
    if not app_java.exists():
        print("❌ 找不到app/src/main/java目录")
        return
    
    # 检查ViewBinding/DataBinding
    print("🔧 检查项目配置...")
    has_view_binding = check_view_binding(project_path)
    has_data_binding = check_data_binding(project_path)
    
    if has_view_binding:
        print("✅ 检测到ViewBinding已启用")
    if has_data_binding:
        print("✅ 检测到DataBinding已启用")
    
    # 收集所有资源文件
    print("📸 收集资源文件...")
    resource_files = []
    for file_path in app_res.rglob("*"):
        if file_path.is_file():
            resource_files.append(file_path)
    
    print(f"   找到 {len(resource_files)} 个资源文件")
    
    # 收集所有代码文件
    print("💻 收集代码文件...")
    code_files = []
    for file_path in app_java.rglob("*"):
        if file_path.is_file() and file_path.suffix in {'.java', '.kt'}:
            code_files.append(file_path)
    
    # 添加XML文件到检查列表
    xml_files = []
    for file_path in app_res.rglob("*.xml"):
        xml_files.append(file_path)
    
    all_check_files = code_files + xml_files
    print(f"   找到 {len(code_files)} 个代码文件, {len(xml_files)} 个XML文件")
    
    # 生成ViewBinding映射
    print("🔗 生成ViewBinding映射...")
    binding_mappings = {}
    layout_files = list(app_res.glob("layout*/*.xml"))
    for layout_file in layout_files:
        layout_name = layout_file.stem
        binding_class = layout_to_binding_class(layout_name)
        binding_mappings[layout_name] = binding_class
    
    print(f"   生成了 {len(binding_mappings)} 个Binding映射")
    
    # 读取所有文件内容（带进度）
    print("📖 读取文件内容...")
    all_content_map = {}
    total_files = len(all_check_files)
    
    for i, file_path in enumerate(all_check_files):
        print_progress(i + 1, total_files, "读取文件")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                all_content_map[str(file_path)] = f.read()
        except Exception:
            all_content_map[str(file_path)] = ""
    
    print(f"\n   ✅ 读取了 {len(all_content_map)} 个文件")
    
    # 合并所有内容用于快速搜索
    print("🔄 合并内容索引...")
    all_content = " ".join(all_content_map.values())
    
    # 分析资源文件使用情况（带进度）
    print("🔍 分析资源文件使用情况...")
    unused_resources = []
    total_resources = len(resource_files)
    
    for i, file_path in enumerate(resource_files):
        print_progress(i + 1, total_resources, "分析资源")
        
        resource_name = file_path.stem
        file_name = file_path.name
        
        is_used = check_resource_usage_smart(
            file_path, resource_name, file_name, 
            all_content, all_content_map, binding_mappings
        )
        
        if not is_used:
            unused_resources.append({
                'name': file_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_res)),
                'size': file_path.stat().st_size,
                'type': get_resource_type(file_path)
            })
    
    print(f"\n   ✅ 发现 {len(unused_resources)} 个可能未使用的资源文件")
    
    # 分析代码文件使用情况（带进度）
    print("🔍 分析代码文件使用情况...")
    unused_classes = []
    total_code_files = len(code_files)
    
    for i, file_path in enumerate(code_files):
        print_progress(i + 1, total_code_files, "分析代码")
        
        class_name = extract_class_name_smart(file_path, all_content_map)
        
        # 检查类是否被使用（排除自身文件）
        file_content = all_content_map.get(str(file_path), "")
        other_content = all_content.replace(file_content, "")
        
        is_used = check_class_usage_smart(class_name, other_content)
        
        if not is_used:
            unused_classes.append({
                'name': file_path.name,
                'class_name': class_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_java)),
                'size': file_path.stat().st_size,
                'type': file_path.suffix
            })
    
    print(f"\n   ✅ 发现 {len(unused_classes)} 个可能未使用的类文件")
    
    # 生成报告
    print("📊 生成报告...")
    generate_smart_report(
        project_path, unused_resources, unused_classes, 
        binding_mappings, has_view_binding, has_data_binding
    )
    
    print(f"\n✅ 智能扫描完成！")
    print(f"📊 可能未使用的资源文件: {len(unused_resources)} 个")
    print(f"📊 可能未使用的类文件: {len(unused_classes)} 个")

def check_view_binding(project_path):
    """检查是否启用了ViewBinding"""
    build_files = [
        project_path / "app" / "build.gradle",
        project_path / "app" / "build.gradle.kts"
    ]
    
    for build_file in build_files:
        if build_file.exists():
            try:
                with open(build_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'viewBinding' in content and 'true' in content:
                        return True
            except:
                pass
    return False

def check_data_binding(project_path):
    """检查是否启用了DataBinding"""
    build_files = [
        project_path / "app" / "build.gradle",
        project_path / "app" / "build.gradle.kts"
    ]
    
    for build_file in build_files:
        if build_file.exists():
            try:
                with open(build_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'dataBinding' in content and 'true' in content:
                        return True
            except:
                pass
    return False

def check_resource_usage_smart(file_path, resource_name, file_name, all_content, content_map, binding_mappings):
    """智能检查资源使用情况"""
    # 1. 传统引用检查
    traditional_checks = [
        f"R.drawable.{resource_name}",
        f"R.layout.{resource_name}",
        f"R.string.{resource_name}",
        f"R.id.{resource_name}",
        f"@drawable/{resource_name}",
        f"@layout/{resource_name}",
        f"@string/{resource_name}",
        f'"{resource_name}"',
        f"'{resource_name}'",
        file_name
    ]
    
    for check in traditional_checks:
        if check in all_content:
            return True
    
    # 2. ViewBinding检查（针对布局文件）
    if file_path.parent.name.startswith('layout'):
        binding_class = binding_mappings.get(resource_name, "")
        if binding_class:
            binding_checks = [
                binding_class,
                f"databinding.{binding_class}",
                f"binding.{binding_class}"
            ]
            for check in binding_checks:
                if check in all_content:
                    return True
    
    # 3. ID引用检查（ViewBinding会为每个ID生成属性）
    if file_path.suffix == '.xml':
        xml_content = content_map.get(str(file_path), "")
        # 提取XML中的所有ID
        id_matches = re.findall(r'android:id="@\+id/(\w+)"', xml_content)
        for id_name in id_matches:
            id_checks = [
                f".{id_name}",  # binding.idName
                f"R.id.{id_name}"  # R.id.idName
            ]
            for check in id_checks:
                if check in all_content:
                    return True
    
    return False

def check_class_usage_smart(class_name, content):
    """智能检查类使用情况"""
    checks = [
        class_name,
        f"import {class_name}",
        f"import .{class_name}",
        f".{class_name}",
        f"extends {class_name}",
        f": {class_name}",  # Kotlin继承
        f"android:name=\"{class_name}\"",
        f"android:name=\".{class_name}\""
    ]
    
    for check in checks:
        if check in content:
            return True
    
    return False

def extract_class_name_smart(file_path, content_map):
    """智能提取类名"""
    content = content_map.get(str(file_path), "")
    if content:
        # 匹配类、接口、枚举、object声明
        patterns = [
            r'(?:public\s+)?(?:class|interface|enum|object)\s+(\w+)',
            r'class\s+(\w+)\s*[:\(]',  # Kotlin类
            r'interface\s+(\w+)\s*[:\{]',  # 接口
            r'enum\s+class\s+(\w+)',  # Kotlin枚举
            r'object\s+(\w+)'  # Kotlin object
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
    
    return file_path.stem

def get_resource_type(file_path):
    """获取资源类型"""
    parent_dir = file_path.parent.name
    extension = file_path.suffix.lower()
    
    if extension in {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}:
        return f"image/{parent_dir}"
    elif extension == '.xml':
        if parent_dir.startswith('layout'):
            return "layout"
        elif parent_dir.startswith('drawable'):
            return "drawable"
        elif parent_dir.startswith('values'):
            return "values"
        elif parent_dir.startswith('anim'):
            return "animation"
        else:
            return f"xml/{parent_dir}"
    else:
        return f"other/{parent_dir}"
