#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源和类文件扫描器
扫描无用的图片、XML资源文件和Java/Kotlin类文件
"""

import os
import re
import json
import time
from pathlib import Path
from typing import Set, Dict, List, Tuple
from collections import defaultdict

class AndroidUnusedResourcesScanner:
    def __init__(self, project_root: str, modules: List[str] = None):
        self.project_root = Path(project_root)

        # 支持多模块扫描，默认只扫描app模块
        self.modules = modules or ["app"]
        self.module_paths = {}

        # 初始化模块路径
        for module in self.modules:
            module_path = self.project_root / module
            if module_path.exists():
                self.module_paths[module] = {
                    'root': module_path,
                    'src_main': module_path / "src" / "main",
                    'java_src': module_path / "src" / "main" / "java",
                    'res_dir': module_path / "src" / "main" / "res",
                    'manifest': module_path / "src" / "main" / "AndroidManifest.xml"
                }

        # 为了兼容性，保留原有的路径（指向app模块）
        if "app" in self.module_paths:
            self.app_src_main = self.module_paths["app"]["src_main"]
            self.java_src = self.module_paths["app"]["java_src"]
            self.res_dir = self.module_paths["app"]["res_dir"]
            self.manifest_file = self.module_paths["app"]["manifest"]
        
        # 存储扫描结果
        self.unused_resources = []
        self.unused_classes = []
        self.resource_references = defaultdict(set)
        self.class_references = defaultdict(set)

        # Git信息缓存
        self.git_info_cache = {}

        # 资源文件扩展名
        self.image_extensions = {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}
        self.xml_extensions = {'.xml'}
        self.code_extensions = {'.java', '.kt'}
        
    def scan_all(self):
        """执行完整扫描"""
        print("🔍 开始扫描Android项目...")
        print(f"📁 项目根目录: {self.project_root}")
        print(f"📦 扫描模块: {', '.join(self.modules)}")

        # 检查Git仓库
        if self._is_git_repo():
            print("📋 检测到Git仓库，将获取文件提交信息...")
        else:
            print("⚠️ 未检测到Git仓库，无法获取提交信息")

        # 扫描资源文件
        print("\n📸 扫描资源文件...")
        self._scan_resources()

        # 扫描代码文件
        print("\n💻 扫描代码文件...")
        self._scan_code_files()

        # 分析引用关系
        print("\n🔗 分析引用关系...")
        self._analyze_references()

        # 生成报告
        print("\n📊 生成扫描报告...")
        self._generate_report()

        print("\n✅ 扫描完成！")
        
    def _scan_resources(self):
        """扫描资源文件"""
        resource_files = []

        # 扫描所有模块的资源目录
        for module_name, paths in self.module_paths.items():
            res_dir = paths['res_dir']
            if not res_dir.exists():
                print(f"⚠️ 模块 {module_name} 的资源目录不存在: {res_dir}")
                continue

            print(f"📂 扫描模块 {module_name} 的资源文件...")

            # 扫描该模块的所有资源目录
            module_files = []
            for res_subdir in res_dir.iterdir():
                if res_subdir.is_dir():
                    for file_path in res_subdir.rglob("*"):
                        if file_path.is_file():
                            module_files.append((file_path, module_name, res_dir))

            print(f"   找到 {len(module_files)} 个资源文件")
            resource_files.extend(module_files)

        print(f"📋 总计找到 {len(resource_files)} 个资源文件")

        # 分类资源文件并获取Git信息
        for file_path, module_name, res_dir in resource_files:
            relative_path = file_path.relative_to(res_dir)

            # 获取Git信息
            git_info = self._get_git_info(str(file_path))
            contributors = self._get_file_contributors(str(file_path))

            file_info = {
                'path': str(file_path),
                'relative_path': str(relative_path),
                'module': module_name,
                'name': file_path.name,
                'size': file_path.stat().st_size,
                'type': self._get_resource_type(file_path),
                'references': set(),
                'git_info': git_info,
                'contributors': contributors
            }

            # 暂时添加到未使用列表，后续分析引用时会移除被引用的
            self.unused_resources.append(file_info)
    
    def _scan_code_files(self):
        """扫描代码文件"""
        code_files = []

        # 扫描所有模块的代码目录
        for module_name, paths in self.module_paths.items():
            java_src = paths['java_src']
            if not java_src.exists():
                print(f"⚠️ 模块 {module_name} 的Java源码目录不存在: {java_src}")
                continue

            print(f"📂 扫描模块 {module_name} 的代码文件...")

            # 扫描该模块的所有Java/Kotlin文件
            module_files = []
            for file_path in java_src.rglob("*"):
                if file_path.is_file() and file_path.suffix in self.code_extensions:
                    module_files.append((file_path, module_name, java_src))

            print(f"   找到 {len(module_files)} 个代码文件")
            code_files.extend(module_files)

        print(f"📋 总计找到 {len(code_files)} 个代码文件")

        # 分析代码文件并获取Git信息
        for file_path, module_name, java_src in code_files:
            relative_path = file_path.relative_to(java_src)
            class_name = self._extract_class_name(file_path)

            # 获取Git信息
            git_info = self._get_git_info(str(file_path))
            contributors = self._get_file_contributors(str(file_path))

            file_info = {
                'path': str(file_path),
                'relative_path': str(relative_path),
                'module': module_name,
                'name': file_path.name,
                'class_name': class_name,
                'size': file_path.stat().st_size,
                'type': file_path.suffix,
                'references': set(),
                'git_info': git_info,
                'contributors': contributors
            }

            # 暂时添加到未使用列表
            self.unused_classes.append(file_info)
    
    def _get_resource_type(self, file_path: Path) -> str:
        """获取资源类型"""
        parent_dir = file_path.parent.name
        extension = file_path.suffix.lower()
        
        if extension in self.image_extensions:
            return f"image/{parent_dir}"
        elif extension in self.xml_extensions:
            if parent_dir.startswith('layout'):
                return "layout"
            elif parent_dir.startswith('drawable'):
                return "drawable"
            elif parent_dir.startswith('values'):
                return "values"
            elif parent_dir.startswith('anim'):
                return "animation"
            else:
                return f"xml/{parent_dir}"
        else:
            return f"other/{parent_dir}"
    
    def _extract_class_name(self, file_path: Path) -> str:
        """从文件路径提取类名"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找类声明
            class_pattern = r'(?:public\s+)?(?:class|interface|enum)\s+(\w+)'
            match = re.search(class_pattern, content)
            if match:
                return match.group(1)
            else:
                # 如果找不到类声明，使用文件名
                return file_path.stem
        except Exception as e:
            print(f"⚠️ 读取文件失败 {file_path}: {e}")
            return file_path.stem
    
    def _analyze_references(self):
        """分析引用关系"""
        print("🔍 分析资源引用...")
        self._analyze_resource_references()
        
        print("🔍 分析类引用...")
        self._analyze_class_references()
        
        # 移除被引用的文件
        self._filter_unused_files()
    
    def _analyze_resource_references(self):
        """分析资源文件引用"""
        # 收集所有需要检查的文件
        files_to_check = []

        # 扫描所有模块的文件
        for module_name, paths in self.module_paths.items():
            # Java/Kotlin文件
            if paths['java_src'].exists():
                files_to_check.extend(paths['java_src'].rglob("*.java"))
                files_to_check.extend(paths['java_src'].rglob("*.kt"))

            # XML文件
            if paths['res_dir'].exists():
                files_to_check.extend(paths['res_dir'].rglob("*.xml"))

            # AndroidManifest.xml
            if paths['manifest'].exists():
                files_to_check.append(paths['manifest'])
        
        # 检查每个资源文件的引用
        for resource_info in self.unused_resources:
            resource_name = Path(resource_info['name']).stem
            
            # 在所有文件中搜索引用
            for file_path in files_to_check:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # 检查各种引用模式
                    patterns = [
                        rf'R\.[\w]+\.{re.escape(resource_name)}\b',  # R.drawable.xxx
                        rf'@[\w]+/{re.escape(resource_name)}\b',     # @drawable/xxx
                        rf'"{re.escape(resource_name)}"',           # "resource_name"
                        rf"'{re.escape(resource_name)}'",           # 'resource_name'
                        rf'{re.escape(resource_info["name"])}',     # 完整文件名
                    ]
                    
                    for pattern in patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            resource_info['references'].add(str(file_path))
                            break
                            
                except Exception as e:
                    continue
    
    def _analyze_class_references(self):
        """分析类文件引用"""
        # 收集所有需要检查的文件
        files_to_check = []

        # 扫描所有模块的文件
        for module_name, paths in self.module_paths.items():
            # Java/Kotlin文件
            if paths['java_src'].exists():
                files_to_check.extend(paths['java_src'].rglob("*.java"))
                files_to_check.extend(paths['java_src'].rglob("*.kt"))

            # AndroidManifest.xml
            if paths['manifest'].exists():
                files_to_check.append(paths['manifest'])
        
        # 检查每个类文件的引用
        for class_info in self.unused_classes:
            class_name = class_info['class_name']
            
            # 在所有文件中搜索引用
            for file_path in files_to_check:
                # 跳过自身文件
                if str(file_path) == class_info['path']:
                    continue
                    
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查各种引用模式
                    patterns = [
                        rf'\b{re.escape(class_name)}\b',           # 类名
                        rf'import\s+.*\.{re.escape(class_name)}\b', # import语句
                        rf'android:name=".*{re.escape(class_name)}"', # AndroidManifest中的声明
                    ]
                    
                    for pattern in patterns:
                        if re.search(pattern, content):
                            class_info['references'].add(str(file_path))
                            break
                            
                except Exception as e:
                    continue
    
    def _filter_unused_files(self):
        """过滤出真正未使用的文件"""
        # 过滤资源文件
        self.unused_resources = [
            res for res in self.unused_resources 
            if len(res['references']) == 0
        ]
        
        # 过滤类文件
        self.unused_classes = [
            cls for cls in self.unused_classes 
            if len(cls['references']) == 0
        ]
        
        print(f"📊 发现 {len(self.unused_resources)} 个未使用的资源文件")
        print(f"📊 发现 {len(self.unused_classes)} 个未使用的类文件")

    def _is_git_repo(self) -> bool:
        """检查是否为Git仓库"""
        return (self.project_root / ".git").exists()

    def _get_git_info(self, file_path: str) -> Dict:
        """获取文件的Git提交信息"""
        if file_path in self.git_info_cache:
            return self.git_info_cache[file_path]

        if not self._is_git_repo():
            return {}

        try:
            import subprocess

            # 获取文件的最后提交信息
            cmd = [
                'git', 'log', '-1', '--pretty=format:%H|%an|%ae|%ad|%s',
                '--date=iso', '--', file_path
            ]

            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and result.stdout.strip():
                parts = result.stdout.strip().split('|', 4)
                if len(parts) >= 5:
                    git_info = {
                        'commit_hash': parts[0][:8],  # 短hash
                        'author_name': parts[1],
                        'author_email': parts[2],
                        'commit_date': parts[3],
                        'commit_message': parts[4]
                    }
                else:
                    git_info = {'error': 'Invalid git log output'}
            else:
                git_info = {'error': 'No git history found'}

        except Exception as e:
            git_info = {'error': f'Git command failed: {str(e)}'}

        self.git_info_cache[file_path] = git_info
        return git_info

    def _get_file_contributors(self, file_path: str) -> List[Dict]:
        """获取文件的所有贡献者信息"""
        if not self._is_git_repo():
            return []

        try:
            import subprocess

            # 获取文件的所有贡献者
            cmd = [
                'git', 'shortlog', '-sne', '--', file_path
            ]

            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=10
            )

            contributors = []
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    # <AUTHOR> <EMAIL>"
                    match = re.match(r'\s*(\d+)\s+(.+?)\s+<(.+?)>', line.strip())
                    if match:
                        contributors.append({
                            'commits': int(match.group(1)),
                            'name': match.group(2),
                            'email': match.group(3)
                        })

            return contributors

        except Exception as e:
            return []

    def _generate_report(self):
        """生成扫描报告"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        # 计算统计信息
        total_unused_size = sum(res['size'] for res in self.unused_resources) + \
                           sum(cls['size'] for cls in self.unused_classes)

        # 生成Markdown报告
        report_content = self._generate_markdown_report(timestamp, total_unused_size)

        # 保存报告
        report_file = self.project_root / f"unused_resources_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 生成JSON报告（便于程序处理）
        json_report = self._generate_json_report(timestamp, total_unused_size)
        json_file = self.project_root / f"unused_resources_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)

        print(f"📄 报告已生成:")
        print(f"   Markdown: {report_file}")
        print(f"   JSON: {json_file}")

        return report_file, json_file

    def _generate_markdown_report(self, timestamp: str, total_size: int) -> str:
        """生成Markdown格式报告"""
        report = f"""# Android项目无用资源扫描报告

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {self.project_root}
- **未使用资源文件**: {len(self.unused_resources)} 个
- **未使用类文件**: {len(self.unused_classes)} 个
- **总计可节省空间**: {self._format_size(total_size)}

## 📸 未使用的资源文件

"""

        if self.unused_resources:
            # 按模块和类型分组
            resources_by_module = defaultdict(lambda: defaultdict(list))
            for res in self.unused_resources:
                module = res.get('module', 'unknown')
                resources_by_module[module][res['type']].append(res)

            for module_name, resources_by_type in resources_by_module.items():
                report += f"### 模块: {module_name}\n\n"

                for res_type, resources in resources_by_type.items():
                    report += f"#### {res_type.title()} ({len(resources)} 个文件)\n\n"
                    report += "| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |\n"
                    report += "|--------|------|------|-----------|----------|----------|\n"

                    for res in sorted(resources, key=lambda x: x['size'], reverse=True):
                        git_info = res.get('git_info', {})
                        author = git_info.get('author_name', '未知')
                        commit_date = git_info.get('commit_date', '未知')
                        commit_msg = git_info.get('commit_message', '未知')

                        # 截断过长的提交信息
                        if len(commit_msg) > 50:
                            commit_msg = commit_msg[:47] + "..."

                        report += f"| {res['name']} | {res['relative_path']} | {self._format_size(res['size'])} | {author} | {commit_date[:10] if commit_date != '未知' else '未知'} | {commit_msg} |\n"

                    report += "\n"

                # 添加贡献者统计
                report += "#### 贡献者统计\n\n"
                all_contributors = defaultdict(int)
                for resources in resources_by_type.values():
                    for res in resources:
                        for contributor in res.get('contributors', []):
                            all_contributors[f"{contributor['name']} <{contributor['email']}>"] += contributor['commits']

                if all_contributors:
                    report += "| 贡献者 | 提交次数 |\n"
                    report += "|--------|----------|\n"
                    for contributor, commits in sorted(all_contributors.items(), key=lambda x: x[1], reverse=True):
                        report += f"| {contributor} | {commits} |\n"
                    report += "\n"
                else:
                    report += "无Git提交信息\n\n"
        else:
            report += "✅ 没有发现未使用的资源文件\n\n"

        report += "## 💻 未使用的类文件\n\n"

        if self.unused_classes:
            # 按模块和类型分组
            classes_by_module = defaultdict(lambda: defaultdict(list))
            for cls in self.unused_classes:
                module = cls.get('module', 'unknown')
                classes_by_module[module][cls['type']].append(cls)

            for module_name, classes_by_type in classes_by_module.items():
                report += f"### 模块: {module_name}\n\n"

                for cls_type, classes in classes_by_type.items():
                    type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
                    report += f"#### {type_name} ({len(classes)} 个文件)\n\n"
                    report += "| 类名 | 文件路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |\n"
                    report += "|------|----------|------|-----------|----------|----------|\n"

                    for cls in sorted(classes, key=lambda x: x['size'], reverse=True):
                        git_info = cls.get('git_info', {})
                        author = git_info.get('author_name', '未知')
                        commit_date = git_info.get('commit_date', '未知')
                        commit_msg = git_info.get('commit_message', '未知')

                        # 截断过长的提交信息
                        if len(commit_msg) > 50:
                            commit_msg = commit_msg[:47] + "..."

                        report += f"| {cls['class_name']} | {cls['relative_path']} | {self._format_size(cls['size'])} | {author} | {commit_date[:10] if commit_date != '未知' else '未知'} | {commit_msg} |\n"

                    report += "\n"

                # 添加贡献者统计
                report += "#### 贡献者统计\n\n"
                all_contributors = defaultdict(int)
                for classes in classes_by_type.values():
                    for cls in classes:
                        for contributor in cls.get('contributors', []):
                            all_contributors[f"{contributor['name']} <{contributor['email']}>"] += contributor['commits']

                if all_contributors:
                    report += "| 贡献者 | 提交次数 |\n"
                    report += "|--------|----------|\n"
                    for contributor, commits in sorted(all_contributors.items(), key=lambda x: x[1], reverse=True):
                        report += f"| {contributor} | {commits} |\n"
                    report += "\n"
                else:
                    report += "无Git提交信息\n\n"
        else:
            report += "✅ 没有发现未使用的类文件\n\n"

        # 添加建议
        report += """## 🔧 清理建议

### 资源文件清理
1. **谨慎删除**: 在删除资源文件前，请仔细检查是否有动态引用（如通过字符串拼接的资源名）
2. **备份项目**: 建议在清理前创建项目备份
3. **分批清理**: 建议分批删除并测试，避免一次性删除过多文件
4. **检查第三方库**: 某些资源可能被第三方库使用，删除前请确认

### 类文件清理
1. **检查反射调用**: 某些类可能通过反射调用，静态分析无法检测到
2. **检查配置文件**: 类可能在配置文件或注解中被引用
3. **检查测试代码**: 确认类没有在测试代码中使用
4. **检查动态加载**: 某些类可能通过动态加载使用

### 自动化清理脚本
可以使用以下命令批量删除未使用的文件（请谨慎使用）：

```bash
# 删除未使用的资源文件（示例）
# 请根据实际情况修改路径
"""

        # 添加删除命令示例
        if self.unused_resources:
            report += "\n# 删除未使用的资源文件\n"
            for res in self.unused_resources[:5]:  # 只显示前5个作为示例
                report += f"# rm \"{res['path']}\"\n"
            if len(self.unused_resources) > 5:
                report += f"# ... 还有 {len(self.unused_resources) - 5} 个文件\n"

        if self.unused_classes:
            report += "\n# 删除未使用的类文件\n"
            for cls in self.unused_classes[:5]:  # 只显示前5个作为示例
                report += f"# rm \"{cls['path']}\"\n"
            if len(self.unused_classes) > 5:
                report += f"# ... 还有 {len(self.unused_classes) - 5} 个文件\n"

        report += "```\n\n"
        report += "⚠️ **警告**: 请在删除前仔细检查每个文件，确保不会影响项目正常运行！\n"

        return report

    def _generate_json_report(self, timestamp: str, total_size: int) -> dict:
        """生成JSON格式报告"""
        return {
            "scan_info": {
                "timestamp": timestamp,
                "project_path": str(self.project_root),
                "total_unused_resources": len(self.unused_resources),
                "total_unused_classes": len(self.unused_classes),
                "total_size_bytes": total_size,
                "total_size_formatted": self._format_size(total_size)
            },
            "unused_resources": self.unused_resources,
            "unused_classes": self.unused_classes
        }

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Android项目无用资源和类文件扫描器')
    parser.add_argument('project_path', nargs='?', default='.',
                       help='Android项目根目录路径 (默认: 当前目录)')
    parser.add_argument('--modules', '-m', nargs='+', default=['app'],
                       help='要扫描的模块列表 (默认: app)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出')
    parser.add_argument('--no-git', action='store_true',
                       help='跳过Git信息获取（加快扫描速度）')

    args = parser.parse_args()

    # 检查项目路径
    project_path = Path(args.project_path).resolve()
    if not project_path.exists():
        print(f"❌ 项目路径不存在: {project_path}")
        return 1

    # 检查是否为Android项目
    if not (project_path / "app" / "build.gradle").exists() and \
       not (project_path / "app" / "build.gradle.kts").exists():
        print(f"❌ 指定路径不是有效的Android项目: {project_path}")
        print("请确保路径包含 app/build.gradle 或 app/build.gradle.kts 文件")
        return 1

    try:
        # 验证模块是否存在
        valid_modules = []
        for module in args.modules:
            module_path = project_path / module
            if module_path.exists():
                valid_modules.append(module)
                print(f"✅ 找到模块: {module}")
            else:
                print(f"⚠️ 模块不存在，跳过: {module}")

        if not valid_modules:
            print("❌ 没有找到有效的模块")
            return 1

        # 创建扫描器并执行扫描
        scanner = AndroidUnusedResourcesScanner(str(project_path), valid_modules)

        # 如果指定了--no-git，禁用Git功能
        if args.no_git:
            scanner._is_git_repo = lambda: False
            print("⚠️ 已禁用Git信息获取")

        scanner.scan_all()

        print("\n🎉 扫描完成！请查看生成的报告文件。")
        return 0

    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 扫描过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
