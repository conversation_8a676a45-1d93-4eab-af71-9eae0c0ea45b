#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源和类文件扫描器
扫描无用的图片、XML资源文件和Java/Kotlin类文件
"""

import os
import re
import json
import time
from pathlib import Path
from typing import Set, Dict, List, Tuple
from collections import defaultdict

class AndroidUnusedResourcesScanner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.app_src_main = self.project_root / "app" / "src" / "main"
        self.java_src = self.app_src_main / "java"
        self.res_dir = self.app_src_main / "res"
        self.manifest_file = self.app_src_main / "AndroidManifest.xml"
        
        # 存储扫描结果
        self.unused_resources = []
        self.unused_classes = []
        self.resource_references = defaultdict(set)
        self.class_references = defaultdict(set)
        
        # 资源文件扩展名
        self.image_extensions = {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}
        self.xml_extensions = {'.xml'}
        self.code_extensions = {'.java', '.kt'}
        
    def scan_all(self):
        """执行完整扫描"""
        print("🔍 开始扫描Android项目...")
        print(f"📁 项目根目录: {self.project_root}")
        
        # 扫描资源文件
        print("\n📸 扫描资源文件...")
        self._scan_resources()
        
        # 扫描代码文件
        print("\n💻 扫描代码文件...")
        self._scan_code_files()
        
        # 分析引用关系
        print("\n🔗 分析引用关系...")
        self._analyze_references()
        
        # 生成报告
        print("\n📊 生成扫描报告...")
        self._generate_report()
        
        print("\n✅ 扫描完成！")
        
    def _scan_resources(self):
        """扫描资源文件"""
        if not self.res_dir.exists():
            print(f"❌ 资源目录不存在: {self.res_dir}")
            return
            
        resource_files = []
        
        # 扫描所有资源目录
        for res_subdir in self.res_dir.iterdir():
            if res_subdir.is_dir():
                for file_path in res_subdir.rglob("*"):
                    if file_path.is_file():
                        resource_files.append(file_path)
        
        print(f"📋 找到 {len(resource_files)} 个资源文件")
        
        # 分类资源文件
        for file_path in resource_files:
            relative_path = file_path.relative_to(self.res_dir)
            file_info = {
                'path': str(file_path),
                'relative_path': str(relative_path),
                'name': file_path.name,
                'size': file_path.stat().st_size,
                'type': self._get_resource_type(file_path),
                'references': set()
            }
            
            # 暂时添加到未使用列表，后续分析引用时会移除被引用的
            self.unused_resources.append(file_info)
    
    def _scan_code_files(self):
        """扫描代码文件"""
        if not self.java_src.exists():
            print(f"❌ Java源码目录不存在: {self.java_src}")
            return
            
        code_files = []
        
        # 扫描所有Java/Kotlin文件
        for file_path in self.java_src.rglob("*"):
            if file_path.is_file() and file_path.suffix in self.code_extensions:
                code_files.append(file_path)
        
        print(f"📋 找到 {len(code_files)} 个代码文件")
        
        # 分析代码文件
        for file_path in code_files:
            relative_path = file_path.relative_to(self.java_src)
            class_name = self._extract_class_name(file_path)
            
            file_info = {
                'path': str(file_path),
                'relative_path': str(relative_path),
                'name': file_path.name,
                'class_name': class_name,
                'size': file_path.stat().st_size,
                'type': file_path.suffix,
                'references': set()
            }
            
            # 暂时添加到未使用列表
            self.unused_classes.append(file_info)
    
    def _get_resource_type(self, file_path: Path) -> str:
        """获取资源类型"""
        parent_dir = file_path.parent.name
        extension = file_path.suffix.lower()
        
        if extension in self.image_extensions:
            return f"image/{parent_dir}"
        elif extension in self.xml_extensions:
            if parent_dir.startswith('layout'):
                return "layout"
            elif parent_dir.startswith('drawable'):
                return "drawable"
            elif parent_dir.startswith('values'):
                return "values"
            elif parent_dir.startswith('anim'):
                return "animation"
            else:
                return f"xml/{parent_dir}"
        else:
            return f"other/{parent_dir}"
    
    def _extract_class_name(self, file_path: Path) -> str:
        """从文件路径提取类名"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找类声明
            class_pattern = r'(?:public\s+)?(?:class|interface|enum)\s+(\w+)'
            match = re.search(class_pattern, content)
            if match:
                return match.group(1)
            else:
                # 如果找不到类声明，使用文件名
                return file_path.stem
        except Exception as e:
            print(f"⚠️ 读取文件失败 {file_path}: {e}")
            return file_path.stem
    
    def _analyze_references(self):
        """分析引用关系"""
        print("🔍 分析资源引用...")
        self._analyze_resource_references()
        
        print("🔍 分析类引用...")
        self._analyze_class_references()
        
        # 移除被引用的文件
        self._filter_unused_files()
    
    def _analyze_resource_references(self):
        """分析资源文件引用"""
        # 收集所有需要检查的文件
        files_to_check = []
        
        # Java/Kotlin文件
        if self.java_src.exists():
            files_to_check.extend(self.java_src.rglob("*.java"))
            files_to_check.extend(self.java_src.rglob("*.kt"))
        
        # XML文件
        if self.res_dir.exists():
            files_to_check.extend(self.res_dir.rglob("*.xml"))
        
        # AndroidManifest.xml
        if self.manifest_file.exists():
            files_to_check.append(self.manifest_file)
        
        # 检查每个资源文件的引用
        for resource_info in self.unused_resources:
            resource_name = Path(resource_info['name']).stem
            
            # 在所有文件中搜索引用
            for file_path in files_to_check:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # 检查各种引用模式
                    patterns = [
                        rf'R\.[\w]+\.{re.escape(resource_name)}\b',  # R.drawable.xxx
                        rf'@[\w]+/{re.escape(resource_name)}\b',     # @drawable/xxx
                        rf'"{re.escape(resource_name)}"',           # "resource_name"
                        rf"'{re.escape(resource_name)}'",           # 'resource_name'
                        rf'{re.escape(resource_info["name"])}',     # 完整文件名
                    ]
                    
                    for pattern in patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            resource_info['references'].add(str(file_path))
                            break
                            
                except Exception as e:
                    continue
    
    def _analyze_class_references(self):
        """分析类文件引用"""
        # 收集所有需要检查的文件
        files_to_check = []
        
        # Java/Kotlin文件
        if self.java_src.exists():
            files_to_check.extend(self.java_src.rglob("*.java"))
            files_to_check.extend(self.java_src.rglob("*.kt"))
        
        # AndroidManifest.xml
        if self.manifest_file.exists():
            files_to_check.append(self.manifest_file)
        
        # 检查每个类文件的引用
        for class_info in self.unused_classes:
            class_name = class_info['class_name']
            
            # 在所有文件中搜索引用
            for file_path in files_to_check:
                # 跳过自身文件
                if str(file_path) == class_info['path']:
                    continue
                    
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查各种引用模式
                    patterns = [
                        rf'\b{re.escape(class_name)}\b',           # 类名
                        rf'import\s+.*\.{re.escape(class_name)}\b', # import语句
                        rf'android:name=".*{re.escape(class_name)}"', # AndroidManifest中的声明
                    ]
                    
                    for pattern in patterns:
                        if re.search(pattern, content):
                            class_info['references'].add(str(file_path))
                            break
                            
                except Exception as e:
                    continue
    
    def _filter_unused_files(self):
        """过滤出真正未使用的文件"""
        # 过滤资源文件
        self.unused_resources = [
            res for res in self.unused_resources 
            if len(res['references']) == 0
        ]
        
        # 过滤类文件
        self.unused_classes = [
            cls for cls in self.unused_classes 
            if len(cls['references']) == 0
        ]
        
        print(f"📊 发现 {len(self.unused_resources)} 个未使用的资源文件")
        print(f"📊 发现 {len(self.unused_classes)} 个未使用的类文件")

    def _generate_report(self):
        """生成扫描报告"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        # 计算统计信息
        total_unused_size = sum(res['size'] for res in self.unused_resources) + \
                           sum(cls['size'] for cls in self.unused_classes)

        # 生成Markdown报告
        report_content = self._generate_markdown_report(timestamp, total_unused_size)

        # 保存报告
        report_file = self.project_root / f"unused_resources_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 生成JSON报告（便于程序处理）
        json_report = self._generate_json_report(timestamp, total_unused_size)
        json_file = self.project_root / f"unused_resources_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)

        print(f"📄 报告已生成:")
        print(f"   Markdown: {report_file}")
        print(f"   JSON: {json_file}")

        return report_file, json_file

    def _generate_markdown_report(self, timestamp: str, total_size: int) -> str:
        """生成Markdown格式报告"""
        report = f"""# Android项目无用资源扫描报告

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {self.project_root}
- **未使用资源文件**: {len(self.unused_resources)} 个
- **未使用类文件**: {len(self.unused_classes)} 个
- **总计可节省空间**: {self._format_size(total_size)}

## 📸 未使用的资源文件

"""

        if self.unused_resources:
            # 按类型分组
            resources_by_type = defaultdict(list)
            for res in self.unused_resources:
                resources_by_type[res['type']].append(res)

            for res_type, resources in resources_by_type.items():
                report += f"### {res_type.title()} ({len(resources)} 个文件)\n\n"
                report += "| 文件名 | 路径 | 大小 |\n"
                report += "|--------|------|------|\n"

                for res in sorted(resources, key=lambda x: x['size'], reverse=True):
                    report += f"| {res['name']} | {res['relative_path']} | {self._format_size(res['size'])} |\n"

                report += "\n"
        else:
            report += "✅ 没有发现未使用的资源文件\n\n"

        report += "## 💻 未使用的类文件\n\n"

        if self.unused_classes:
            # 按类型分组
            classes_by_type = defaultdict(list)
            for cls in self.unused_classes:
                classes_by_type[cls['type']].append(cls)

            for cls_type, classes in classes_by_type.items():
                type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
                report += f"### {type_name} ({len(classes)} 个文件)\n\n"
                report += "| 类名 | 文件路径 | 大小 |\n"
                report += "|------|----------|------|\n"

                for cls in sorted(classes, key=lambda x: x['size'], reverse=True):
                    report += f"| {cls['class_name']} | {cls['relative_path']} | {self._format_size(cls['size'])} |\n"

                report += "\n"
        else:
            report += "✅ 没有发现未使用的类文件\n\n"

        # 添加建议
        report += """## 🔧 清理建议

### 资源文件清理
1. **谨慎删除**: 在删除资源文件前，请仔细检查是否有动态引用（如通过字符串拼接的资源名）
2. **备份项目**: 建议在清理前创建项目备份
3. **分批清理**: 建议分批删除并测试，避免一次性删除过多文件
4. **检查第三方库**: 某些资源可能被第三方库使用，删除前请确认

### 类文件清理
1. **检查反射调用**: 某些类可能通过反射调用，静态分析无法检测到
2. **检查配置文件**: 类可能在配置文件或注解中被引用
3. **检查测试代码**: 确认类没有在测试代码中使用
4. **检查动态加载**: 某些类可能通过动态加载使用

### 自动化清理脚本
可以使用以下命令批量删除未使用的文件（请谨慎使用）：

```bash
# 删除未使用的资源文件（示例）
# 请根据实际情况修改路径
"""

        # 添加删除命令示例
        if self.unused_resources:
            report += "\n# 删除未使用的资源文件\n"
            for res in self.unused_resources[:5]:  # 只显示前5个作为示例
                report += f"# rm \"{res['path']}\"\n"
            if len(self.unused_resources) > 5:
                report += f"# ... 还有 {len(self.unused_resources) - 5} 个文件\n"

        if self.unused_classes:
            report += "\n# 删除未使用的类文件\n"
            for cls in self.unused_classes[:5]:  # 只显示前5个作为示例
                report += f"# rm \"{cls['path']}\"\n"
            if len(self.unused_classes) > 5:
                report += f"# ... 还有 {len(self.unused_classes) - 5} 个文件\n"

        report += "```\n\n"
        report += "⚠️ **警告**: 请在删除前仔细检查每个文件，确保不会影响项目正常运行！\n"

        return report

    def _generate_json_report(self, timestamp: str, total_size: int) -> dict:
        """生成JSON格式报告"""
        return {
            "scan_info": {
                "timestamp": timestamp,
                "project_path": str(self.project_root),
                "total_unused_resources": len(self.unused_resources),
                "total_unused_classes": len(self.unused_classes),
                "total_size_bytes": total_size,
                "total_size_formatted": self._format_size(total_size)
            },
            "unused_resources": self.unused_resources,
            "unused_classes": self.unused_classes
        }

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Android项目无用资源和类文件扫描器')
    parser.add_argument('project_path', nargs='?', default='.',
                       help='Android项目根目录路径 (默认: 当前目录)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出')

    args = parser.parse_args()

    # 检查项目路径
    project_path = Path(args.project_path).resolve()
    if not project_path.exists():
        print(f"❌ 项目路径不存在: {project_path}")
        return 1

    # 检查是否为Android项目
    if not (project_path / "app" / "build.gradle").exists() and \
       not (project_path / "app" / "build.gradle.kts").exists():
        print(f"❌ 指定路径不是有效的Android项目: {project_path}")
        print("请确保路径包含 app/build.gradle 或 app/build.gradle.kts 文件")
        return 1

    try:
        # 创建扫描器并执行扫描
        scanner = AndroidUnusedResourcesScanner(str(project_path))
        scanner.scan_all()

        print("\n🎉 扫描完成！请查看生成的报告文件。")
        return 0

    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 扫描过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
