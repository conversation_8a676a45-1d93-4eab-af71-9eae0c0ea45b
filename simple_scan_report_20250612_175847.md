# Android项目无用资源扫描报告（简单模式）

## 扫描信息
- **扫描时间**: 2025-06-12 17:58:47
- **项目路径**: .
- **可能未使用的资源文件**: 394 个
- **可能未使用的类文件**: 75 个
- **总计可能节省空间**: 6.4 MB

⚠️ **注意**: 这是简单模式扫描，可能存在误报。请仔细检查每个文件后再决定是否删除。

## 📸 可能未使用的资源文件

### Other/Res (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| .DS_Store | .DS_Store | 6.0 KB |

### Image/Mipmap-Mdpi (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| ic_launcher1.webp | mipmap-mdpi/ic_launcher1.webp | 2.2 KB |

### Values (27 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| strings.xml | values-th-rTH/strings.xml | 279.1 KB |
| strings.xml | values-hi-rIN/strings.xml | 278.4 KB |
| strings.xml | values-ru-rRU/strings.xml | 243.1 KB |
| strings.xml | values-kk-rKZ/strings.xml | 235.1 KB |
| strings.xml | values-mn-rMN/strings.xml | 231.0 KB |
| strings.xml | values-ar-rSA/strings.xml | 206.8 KB |
| strings.xml | values-vi-rVN/strings.xml | 193.0 KB |
| strings.xml | values-ja-rJP/strings.xml | 188.3 KB |
| strings.xml | values-fr-rFR/strings.xml | 181.6 KB |
| strings.xml | values-tl-rPH/strings.xml | 179.4 KB |
| strings.xml | values-de-rDE/strings.xml | 178.8 KB |
| strings.xml | values-es-rES/strings.xml | 178.1 KB |
| strings.xml | values-pt-rPT/strings.xml | 175.4 KB |
| strings.xml | values-it-rIT/strings.xml | 175.3 KB |
| strings.xml | values-ko-rKR/strings.xml | 175.0 KB |
| strings.xml | values-in-rID/strings.xml | 169.7 KB |
| strings.xml | values-ms-rMY/strings.xml | 169.6 KB |
| strings.xml | values/strings.xml | 163.2 KB |
| strings.xml | values-en/strings.xml | 162.9 KB |
| strings.xml | values-zh-rTW/strings.xml | 151.6 KB |
| ... | ... | 还有 7 个文件 |

### Xml/Anim (2 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| dialog_exit_anim.xml | anim/dialog_exit_anim.xml | 259 B |
| dialog_enter_anim.xml | anim/dialog_enter_anim.xml | 259 B |

### Image/Mipmap-Hdpi (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| ic_launcher1.webp | mipmap-hdpi/ic_launcher1.webp | 3.9 KB |

### Drawable (17 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| select_price_alter_et_bg.xml | drawable/select_price_alter_et_bg.xml | 601 B |
| select_rename_passkey_et_bg.xml | drawable/select_rename_passkey_et_bg.xml | 465 B |
| shape_stroke_ce35728_solid_c1fe35728_r16.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r16.xml | 354 B |
| shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | 354 B |
| draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | 347 B |
| shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | 297 B |
| shape_stroke_ce35728_solid_c1fe35728_r2.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r2.xml | 297 B |
| draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | 284 B |
| draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | 275 B |
| draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | 248 B |
| draw_shape_cffffff_c1a1d20_r10.xml | drawable/draw_shape_cffffff_c1a1d20_r10.xml | 234 B |
| draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | 220 B |
| draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | 217 B |
| draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | 214 B |
| draw_shape_line_c1f1e1e1e_c1fffffff.xml | drawable/draw_shape_line_c1f1e1e1e_c1fffffff.xml | 213 B |
| draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | drawable/draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | 212 B |
| draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | drawable/draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | 207 B |

### Image/Mipmap-Xxxhdpi (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| ic_launcher1.webp | mipmap-xxxhdpi/ic_launcher1.webp | 18.7 KB |

### Layout (331 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| fragment_kline_chart.xml | layout/fragment_kline_chart.xml | 68.3 KB |
| activity_order.xml | layout/activity_order.xml | 59.0 KB |
| activity_modify_order.xml | layout/activity_modify_order.xml | 46.5 KB |
| fragment_kline_chart_new.xml | layout/fragment_kline_chart_new.xml | 40.8 KB |
| activity_st_create_and_edit_strategy.xml | layout/activity_st_create_and_edit_strategy.xml | 39.3 KB |
| fragment_st_trades.xml | layout/fragment_st_trades.xml | 39.0 KB |
| fragment_kline_info.xml | layout/fragment_kline_info.xml | 33.5 KB |
| fragment_st_manual_trading.xml | layout/fragment_st_manual_trading.xml | 30.2 KB |
| activity_position_details.xml | layout/activity_position_details.xml | 30.1 KB |
| fragment_order_theme.xml | layout/fragment_order_theme.xml | 30.0 KB |
| activity_st_strategy_position_details.xml | layout/activity_st_strategy_position_details.xml | 24.4 KB |
| activity_st_strategy_add_or_remove_funds.xml | layout/activity_st_strategy_add_or_remove_funds.xml | 24.1 KB |
| activity_open_same_name_account.xml | layout/activity_open_same_name_account.xml | 23.6 KB |
| activity_open_acount_first_white.xml | layout/activity_open_acount_first_white.xml | 22.7 KB |
| activity_pending_details.xml | layout/activity_pending_details.xml | 21.5 KB |
| activity_funds.xml | layout/activity_funds.xml | 18.5 KB |
| activity_manage_account.xml | layout/activity_manage_account.xml | 18.4 KB |
| activity_create_price_alert.xml | layout/activity_create_price_alert.xml | 18.4 KB |
| activity_st_strategy_details.xml | layout/activity_st_strategy_details.xml | 17.5 KB |
| activity_main_new_comer_event.xml | layout/activity_main_new_comer_event.xml | 16.8 KB |
| ... | ... | 还有 311 个文件 |

### Other/Layout (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| .DS_Store | layout/.DS_Store | 48.0 KB |

### Image/Mipmap-Xxhdpi (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| ic_launcher1.webp | mipmap-xxhdpi/ic_launcher1.webp | 11.3 KB |

### Image/Drawable-Xxhdpi (8 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| img_st_follow_empty.webp | drawable-xxhdpi/img_st_follow_empty.webp | 1.4 KB |
| icon2_profile_transfer_d.webp | drawable-xxhdpi/icon2_profile_transfer_d.webp | 1.2 KB |
| icon2_cb_x_circle_cf44040.webp | drawable-xxhdpi/icon2_cb_x_circle_cf44040.webp | 692 B |
| img_profile_wallet_new.webp | drawable-xxhdpi/img_profile_wallet_new.webp | 590 B |
| icon_source2_info_2_14x14.webp | drawable-xxhdpi/icon_source2_info_2_14x14.webp | 456 B |
| icon_history.webp | drawable-xxhdpi/icon_history.webp | 442 B |
| img_source_coupons.webp | drawable-xxhdpi/img_source_coupons.webp | 324 B |
| img_source_right_8x6.webp | drawable-xxhdpi/img_source_right_8x6.webp | 216 B |

### Xml/Xml (2 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| network_security_config.xml | xml/network_security_config.xml | 396 B |
| file_paths.xml | xml/file_paths.xml | 267 B |

### Image/Mipmap-Xhdpi (1 个文件)

| 文件名 | 路径 | 大小 |
|--------|------|------|
| ic_launcher1.webp | mipmap-xhdpi/ic_launcher1.webp | 5.8 KB |

## 💻 可能未使用的类文件

### Kotlin文件 (64 个文件)

| 类名 | 文件路径 | 大小 |
|------|----------|------|
| StringAdapter | cn/com/vau/util/GsonUtil.kt | 18.3 KB |
| DrawerTradingViewSettingDialogNew | cn/com/vau/trade/kchart/tradingview/DrawerTradingViewSettingDialogNew.kt | 16.0 KB |
| DynamicAdapter | cn/com/vau/util/TabLayoutExt.kt | 12.4 KB |
| MyFirebaseMessagingService | cn/com/vau/common/push/MyFirebaseMessagingService.kt | 11.5 KB |
| KycVerifyViewModel | cn/com/vau/util/KycVerifyHelper.kt | 9.8 KB |
| LibEx | cn/com/vau/common/view/tablayout/LibEx.kt | 9.2 KB |
| Expand2 | cn/com/vau/util/Expand2.kt | 9.0 KB |
| H5DepositBean | cn/com/vau/data/depositcoupon/DepositPayDataFile.kt | 7.2 KB |
| BottomMonthYearDialog | cn/com/vau/util/widget/dialog/BottomMonthYearDialog.kt | 6.1 KB |
| NetworkUtils | cn/com/vau/util/widget/webview/utils/NetworkUtils.kt | 5.4 KB |
| SelectMonthYearDialog | cn/com/vau/common/view/dialog/SelectMonthYearDialog.kt | 5.2 KB |
| SumSubJumpViewModel | cn/com/vau/page/user/sumsub/SumSubJumpHelper.kt | 5.2 KB |
| CouponManagerBean | cn/com/vau/data/depositcoupon/CouponDataFile.kt | 5.0 KB |
| BaseListBottomDialog | cn/com/vau/common/view/dialog/BaseListBottomDialog.kt | 4.7 KB |
| CSConsultBean | cn/com/vau/data/msg/MsgDataFile.kt | 4.6 KB |
| State1 | cn/com/vau/util/LiveDataExt.kt | 3.7 KB |
| LiveActivePopup | cn/com/vau/signals/live/LiveActivePopup.kt | 3.5 KB |
| ProductItemNewsFragment | cn/com/vau/trade/fragment/kchart/ProductItemNewsFragment.kt | 3.4 KB |
| BaseViewModelExt | cn/com/vau/common/mvvm/ext/BaseViewModelExt.kt | 3.2 KB |
| LooperDetectorUtil | cn/com/vau/util/opt/LooperDetectorUtil.kt | 3.1 KB |
| ... | ... | 还有 44 个文件 |

### Java文件 (11 个文件)

| 类名 | 文件路径 | 大小 |
|------|----------|------|
| addresses | cn/com/vau/common/view/InputConnectionAccomodatingLatinIMETypeNullIssues.java | 13.7 KB |
| SearchHistoryAdapter | cn/com/vau/trade/adapter/SearchHistoryAdapter.java | 3.5 KB |
| ChartTypeRecyclerAdapter | cn/com/vau/trade/adapter/ChartTypeRecyclerAdapter.java | 3.4 KB |
| PrefetchLayoutInflaterTradeItemUtil | cn/com/vau/util/PrefetchLayoutInflaterTradeItemUtil.java | 3.2 KB |
| PictureFileUtil | cn/com/vau/util/PictureFileUtil.java | 2.4 KB |
| SerializableOkHttpCookies | cn/com/vau/common/http/cookie/SerializableOkHttpCookies.java | 2.1 KB |
| FpsCounterUtil | cn/com/vau/util/opt/FpsCounterUtil.java | 1.5 KB |
| ErrorLogReportBean | cn/com/vau/common/greendao/dbUtils/ErrorLogReportBean.java | 1.3 KB |
| DataBoundListAdapter | cn/com/vau/common/adapter/DataBoundListAdapter.java | 1.3 KB |
| BaseDialog | cn/com/vau/signals/live/history/base/BaseDialog.java | 1.0 KB |
| ProductDetailsModel | cn/com/vau/trade/model/ProductDetailsModel.java | 0 B |


## ⚠️ 重要提醒

1. **这是简单模式扫描**，使用基本的字符串匹配，可能存在误报
2. **动态引用无法检测**，如通过反射、字符串拼接等方式的引用
3. **建议手动检查**每个标记为"未使用"的文件
4. **删除前备份**整个项目
5. **分批删除**并测试项目功能

## 🔧 下一步建议

1. 手动检查报告中的文件
2. 搜索项目中是否有其他引用方式
3. 确认无误后再删除文件
4. 删除后进行完整测试
