# Android项目无用资源扫描器

这是一个用于扫描Android项目中无用资源文件和类文件的Python工具集，可以帮助您清理项目，减少APK大小。

## 功能特性

- 🔍 **智能扫描**: 自动检测未被引用的资源文件和类文件
- 📊 **详细报告**: 生成Markdown和JSON格式的详细扫描报告
- 🛡️ **安全清理**: 提供备份功能，确保文件安全
- 📱 **Android专用**: 专门针对Android项目结构优化
- 🎯 **精确分析**: 支持多种引用模式检测

## 支持的文件类型

### 资源文件
- 图片资源: `.png`, `.jpg`, `.jpeg`, `.webp`, `.gif`, `.svg`
- XML资源: 布局文件、drawable、values、动画等
- 其他资源: raw、mipmap等

### 代码文件
- Java文件: `.java`
- Kotlin文件: `.kt`

## 安装要求

- Python 3.6+
- Android项目（包含`app/build.gradle`或`app/build.gradle.kts`）

## 使用方法

### 1. 快速开始

将脚本文件复制到您的Android项目根目录，然后运行：

```bash
# 方法1: 使用简化脚本
python run_scanner.py

# 方法2: 直接使用主脚本
python unused_resources_scanner.py

# 方法3: 扫描指定项目
python unused_resources_scanner.py /path/to/your/android/project
```

### 2. 查看扫描报告

扫描完成后，会在项目根目录生成两个报告文件：
- `unused_resources_report_YYYYMMDD_HHMMSS.md` - Markdown格式报告
- `unused_resources_report_YYYYMMDD_HHMMSS.json` - JSON格式报告

### 3. 清理无用文件

⚠️ **重要**: 在清理文件前，请务必备份您的项目！

```bash
# 基本清理（会自动创建备份）
python cleanup_unused_files.py unused_resources_report_YYYYMMDD_HHMMSS.json

# 只清理资源文件
python cleanup_unused_files.py --resources-only report.json

# 只清理类文件
python cleanup_unused_files.py --classes-only report.json

# 不创建备份（危险操作）
python cleanup_unused_files.py --no-backup report.json

# 自动确认（用于脚本自动化）
python cleanup_unused_files.py --auto-confirm report.json
```

## 命令行参数

### unused_resources_scanner.py
```bash
python unused_resources_scanner.py [项目路径] [选项]

参数:
  项目路径              Android项目根目录路径（默认: 当前目录）

选项:
  --verbose, -v        显示详细输出
  --help, -h          显示帮助信息
```

### cleanup_unused_files.py
```bash
python cleanup_unused_files.py 报告文件 [选项]

参数:
  报告文件              扫描报告JSON文件路径

选项:
  --no-backup          不创建备份（危险操作）
  --resources-only     只删除资源文件
  --classes-only       只删除类文件
  --auto-confirm       自动确认删除（危险操作）
  --help, -h          显示帮助信息
```

## 扫描原理

### 资源文件检测
1. 扫描`app/src/main/res`目录下的所有资源文件
2. 在以下位置查找引用：
   - Java/Kotlin源码中的`R.xxx.xxx`引用
   - XML文件中的`@xxx/xxx`引用
   - 字符串形式的资源名引用
   - AndroidManifest.xml中的引用

### 类文件检测
1. 扫描`app/src/main/java`目录下的所有Java/Kotlin文件
2. 在以下位置查找引用：
   - import语句
   - 类名直接引用
   - AndroidManifest.xml中的组件声明

## 注意事项

### ⚠️ 重要警告
1. **动态引用**: 脚本无法检测通过反射、字符串拼接等方式的动态引用
2. **第三方库**: 某些资源可能被第三方库使用
3. **测试代码**: 确保检查测试代码中的引用
4. **配置文件**: 某些类可能在配置文件中被引用

### 🛡️ 安全建议
1. **备份项目**: 在清理前务必备份整个项目
2. **分批清理**: 建议分批删除并测试
3. **仔细检查**: 手动检查报告中的文件
4. **测试运行**: 清理后进行完整的功能测试

## 示例输出

### 扫描过程
```
🔍 开始扫描Android项目...
📁 项目根目录: /path/to/your/project

📸 扫描资源文件...
📋 找到 1250 个资源文件

💻 扫描代码文件...
📋 找到 320 个代码文件

🔗 分析引用关系...
🔍 分析资源引用...
🔍 分析类引用...
📊 发现 45 个未使用的资源文件
📊 发现 12 个未使用的类文件

📊 生成扫描报告...
📄 报告已生成:
   Markdown: unused_resources_report_20231201_143022.md
   JSON: unused_resources_report_20231201_143022.json

✅ 扫描完成！
```

### 清理过程
```
🧹 Android项目无用文件清理工具
==================================================
📄 报告文件: unused_resources_report_20231201_143022.json
📁 项目路径: /path/to/your/project
🗑️ 待删除资源文件: 45 个
🗑️ 待删除类文件: 12 个
💾 可节省空间: 2.3 MB

⚠️ 警告: 此操作将删除检测到的无用文件

是否继续？(y/N): y

📦 创建备份目录: /path/to/your/project/backup_20231201_143500

🗑️ 准备删除 45 个资源文件:
📦 备份: icon_unused.png -> backup_20231201_143500/icon_unused.png
🗑️ 删除: /path/to/res/drawable-xxhdpi/icon_unused.png
...

✅ 清理完成!
📊 删除了 57 个文件
💾 节省空间 2.3 MB
📄 清理报告: cleanup_report_20231201_143500.md
📦 备份目录: /path/to/your/project/backup_20231201_143500
```

## 故障排除

### 常见问题

1. **"不是有效的Android项目"**
   - 确保在Android项目根目录运行
   - 检查是否存在`app/build.gradle`文件

2. **"扫描结果不准确"**
   - 检查是否有动态引用
   - 确认第三方库的使用情况

3. **"删除后项目无法运行"**
   - 从备份目录恢复文件
   - 检查是否有遗漏的引用

### 获取帮助

如果遇到问题，请：
1. 检查Python版本（需要3.6+）
2. 确认项目结构正确
3. 查看详细的错误信息
4. 使用`--verbose`参数获取更多信息

## 许可证

本工具仅供学习和项目优化使用，使用时请遵循相关开源协议。

---

**免责声明**: 使用本工具删除文件前，请务必备份项目。作者不对因使用本工具造成的任何损失承担责任。
