# Android项目无用资源扫描器

这是一个用于扫描Android项目中无用资源文件和类文件的Python工具，可以帮助您分析项目，识别可能的冗余文件。

## 功能特性

- 🔍 **智能扫描**: 自动检测未被引用的资源文件和类文件
- 📊 **详细报告**: 生成Markdown和JSON格式的详细扫描报告，包含Git提交信息
- 📦 **多模块支持**: 支持扫描指定的Android模块（默认只扫描app模块）
- 👥 **Git集成**: 显示文件的最后提交者、提交时间和贡献者统计
- 📱 **Android专用**: 专门针对Android项目结构优化
- 🎯 **精确分析**: 支持多种引用模式检测
- 🛡️ **只读分析**: 仅进行分析，不会自动删除任何文件

## 支持的文件类型

### 资源文件
- 图片资源: `.png`, `.jpg`, `.jpeg`, `.webp`, `.gif`, `.svg`
- XML资源: 布局文件、drawable、values、动画等
- 其他资源: raw、mipmap等

### 代码文件
- Java文件: `.java`
- Kotlin文件: `.kt`

## 安装要求

- Python 3.6+
- Android项目（包含`app/build.gradle`或`app/build.gradle.kts`）

## 使用方法

### 1. 快速开始

将脚本文件复制到您的Android项目根目录，然后运行：

```bash
# 方法1: 使用简化脚本（只扫描app模块）
python run_scanner.py

# 方法2: 直接使用主脚本（只扫描app模块）
python unused_resources_scanner.py

# 方法3: 扫描指定项目
python unused_resources_scanner.py /path/to/your/android/project

# 方法4: 扫描多个模块
python unused_resources_scanner.py --modules app lib_kit kline

# 方法5: 跳过Git信息获取（加快扫描速度）
python unused_resources_scanner.py --no-git
```

### 2. 查看扫描报告

扫描完成后，会在项目根目录生成两个报告文件：
- `unused_resources_report_YYYYMMDD_HHMMSS.md` - Markdown格式报告（包含Git信息）
- `unused_resources_report_YYYYMMDD_HHMMSS.json` - JSON格式报告

报告包含以下信息：
- 未使用的文件列表
- 文件大小统计
- Git最后提交者信息
- 提交时间和提交信息
- 贡献者统计

### 3. 手动清理文件

⚠️ **重要**: 本工具只进行分析，不会自动删除文件。请根据报告手动检查和删除文件。

建议的清理流程：
1. 仔细查看生成的报告
2. 手动检查每个标记为"未使用"的文件
3. 确认文件确实未被使用后再删除
4. 分批删除并测试项目功能

## 命令行参数

### unused_resources_scanner.py
```bash
python unused_resources_scanner.py [项目路径] [选项]

参数:
  项目路径              Android项目根目录路径（默认: 当前目录）

选项:
  --modules, -m        要扫描的模块列表（默认: app）
  --verbose, -v        显示详细输出
  --no-git            跳过Git信息获取（加快扫描速度）
  --help, -h          显示帮助信息

示例:
  python unused_resources_scanner.py                    # 扫描app模块
  python unused_resources_scanner.py --modules app lib_kit  # 扫描多个模块
  python unused_resources_scanner.py --no-git          # 跳过Git信息
```

## 扫描原理

### 资源文件检测
1. 扫描`app/src/main/res`目录下的所有资源文件
2. 在以下位置查找引用：
   - Java/Kotlin源码中的`R.xxx.xxx`引用
   - XML文件中的`@xxx/xxx`引用
   - 字符串形式的资源名引用
   - AndroidManifest.xml中的引用

### 类文件检测
1. 扫描`app/src/main/java`目录下的所有Java/Kotlin文件
2. 在以下位置查找引用：
   - import语句
   - 类名直接引用
   - AndroidManifest.xml中的组件声明

## 注意事项

### ⚠️ 重要警告
1. **动态引用**: 脚本无法检测通过反射、字符串拼接等方式的动态引用
2. **第三方库**: 某些资源可能被第三方库使用
3. **测试代码**: 确保检查测试代码中的引用
4. **配置文件**: 某些类可能在配置文件中被引用

### 🛡️ 安全建议
1. **仔细检查**: 手动检查报告中的每个文件
2. **备份项目**: 在删除文件前务必备份整个项目
3. **分批清理**: 建议分批删除并测试
4. **测试运行**: 删除文件后进行完整的功能测试
5. **检查Git信息**: 利用提交者信息联系相关开发者确认

## 示例输出

### 扫描过程
```
🔍 开始扫描Android项目...
📁 项目根目录: /path/to/your/project
📦 扫描模块: app
📋 检测到Git仓库，将获取文件提交信息...

📸 扫描资源文件...
📂 扫描模块 app 的资源文件...
   找到 1250 个资源文件
📋 总计找到 1250 个资源文件

💻 扫描代码文件...
📂 扫描模块 app 的代码文件...
   找到 320 个代码文件
📋 总计找到 320 个代码文件

🔗 分析引用关系...
🔍 分析资源引用...
🔍 分析类引用...
📊 发现 45 个未使用的资源文件
📊 发现 12 个未使用的类文件

📊 生成扫描报告...
📄 报告已生成:
   Markdown: unused_resources_report_20231201_143022.md
   JSON: unused_resources_report_20231201_143022.json

✅ 扫描完成！
```

### 报告示例
生成的Markdown报告包含以下信息：

```markdown
## 📸 未使用的资源文件

### 模块: app

#### Image/Drawable-Xxhdpi (15 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| icon_unused.png | drawable-xxhdpi/icon_unused.png | 12.3 KB | 张三 | 2023-11-15 | 添加新图标资源 |
| old_logo.webp | drawable-xxhdpi/old_logo.webp | 8.7 KB | 李四 | 2023-10-20 | 更新应用logo |

#### 贡献者统计

| 贡献者 | 提交次数 |
|--------|----------|
| 张三 <<EMAIL>> | 8 |
| 李四 <<EMAIL>> | 5 |
```

## 故障排除

### 常见问题

1. **"不是有效的Android项目"**
   - 确保在Android项目根目录运行
   - 检查是否存在`app/build.gradle`文件

2. **"扫描结果不准确"**
   - 检查是否有动态引用
   - 确认第三方库的使用情况

3. **"删除后项目无法运行"**
   - 从备份目录恢复文件
   - 检查是否有遗漏的引用

### 获取帮助

如果遇到问题，请：
1. 检查Python版本（需要3.6+）
2. 确认项目结构正确
3. 查看详细的错误信息
4. 使用`--verbose`参数获取更多信息

## 许可证

本工具仅供学习和项目优化使用，使用时请遵循相关开源协议。

---

**免责声明**: 使用本工具删除文件前，请务必备份项目。作者不对因使用本工具造成的任何损失承担责任。
