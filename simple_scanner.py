#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源简单扫描器
"""

import os
import re
import time
from pathlib import Path
from collections import defaultdict

def scan_android_project(project_root):
    """扫描Android项目"""
    project_path = Path(project_root)
    app_res = project_path / "app" / "src" / "main" / "res"
    app_java = project_path / "app" / "src" / "main" / "java"
    
    print(f"🔍 扫描项目: {project_path}")
    
    if not app_res.exists():
        print("❌ 找不到app/src/main/res目录")
        return
    
    if not app_java.exists():
        print("❌ 找不到app/src/main/java目录")
        return
    
    # 收集所有资源文件
    print("📸 收集资源文件...")
    resource_files = []
    for file_path in app_res.rglob("*"):
        if file_path.is_file():
            resource_files.append(file_path)
    
    print(f"   找到 {len(resource_files)} 个资源文件")
    
    # 收集所有代码文件
    print("💻 收集代码文件...")
    code_files = []
    for file_path in app_java.rglob("*"):
        if file_path.is_file() and file_path.suffix in {'.java', '.kt'}:
            code_files.append(file_path)
    
    # 添加XML文件到检查列表
    xml_files = []
    for file_path in app_res.rglob("*.xml"):
        xml_files.append(file_path)
    
    all_check_files = code_files + xml_files
    print(f"   找到 {len(code_files)} 个代码文件, {len(xml_files)} 个XML文件")
    
    # 读取所有文件内容
    print("📖 读取文件内容...")
    all_content = ""
    file_count = 0
    
    for file_path in all_check_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                all_content += " " + content
                file_count += 1
                if file_count % 100 == 0:
                    print(f"   已读取 {file_count}/{len(all_check_files)} 个文件")
        except Exception as e:
            continue
    
    print(f"   总共读取了 {file_count} 个文件")
    
    # 分析资源文件使用情况
    print("🔍 分析资源文件使用情况...")
    unused_resources = []
    
    for i, file_path in enumerate(resource_files):
        if i % 200 == 0:
            print(f"   检查进度: {i}/{len(resource_files)}")
        
        resource_name = file_path.stem
        file_name = file_path.name
        
        # 简单的字符串搜索
        is_used = (
            resource_name in all_content or
            file_name in all_content or
            f"R.drawable.{resource_name}" in all_content or
            f"R.layout.{resource_name}" in all_content or
            f"R.string.{resource_name}" in all_content or
            f"@drawable/{resource_name}" in all_content or
            f"@layout/{resource_name}" in all_content or
            f"@string/{resource_name}" in all_content
        )
        
        if not is_used:
            unused_resources.append({
                'name': file_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_res)),
                'size': file_path.stat().st_size,
                'type': get_resource_type(file_path)
            })
    
    # 分析代码文件使用情况
    print("🔍 分析代码文件使用情况...")
    unused_classes = []
    
    for i, file_path in enumerate(code_files):
        if i % 100 == 0:
            print(f"   检查进度: {i}/{len(code_files)}")
        
        class_name = extract_class_name(file_path)
        
        # 简单的字符串搜索（排除自身文件）
        file_content = ""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                file_content = f.read()
        except:
            pass
        
        # 从总内容中移除当前文件内容进行检查
        other_content = all_content.replace(file_content, "")
        
        is_used = (
            class_name in other_content or
            f"import {class_name}" in other_content or
            f".{class_name}" in other_content
        )
        
        if not is_used:
            unused_classes.append({
                'name': file_path.name,
                'class_name': class_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_java)),
                'size': file_path.stat().st_size,
                'type': file_path.suffix
            })
    
    # 生成报告
    print("📊 生成报告...")
    generate_report(project_path, unused_resources, unused_classes)
    
    print(f"✅ 扫描完成！")
    print(f"📊 发现 {len(unused_resources)} 个可能未使用的资源文件")
    print(f"📊 发现 {len(unused_classes)} 个可能未使用的类文件")

def get_resource_type(file_path):
    """获取资源类型"""
    parent_dir = file_path.parent.name
    extension = file_path.suffix.lower()
    
    if extension in {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}:
        return f"image/{parent_dir}"
    elif extension == '.xml':
        if parent_dir.startswith('layout'):
            return "layout"
        elif parent_dir.startswith('drawable'):
            return "drawable"
        elif parent_dir.startswith('values'):
            return "values"
        else:
            return f"xml/{parent_dir}"
    else:
        return f"other/{parent_dir}"

def extract_class_name(file_path):
    """提取类名"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            match = re.search(r'(?:public\s+)?(?:class|interface|enum)\s+(\w+)', content)
            if match:
                return match.group(1)
    except:
        pass
    return file_path.stem

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"

def generate_report(project_path, unused_resources, unused_classes):
    """生成报告"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    total_size = sum(r['size'] for r in unused_resources) + sum(c['size'] for c in unused_classes)
    
    report = f"""# Android项目无用资源扫描报告（简单模式）

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {project_path}
- **可能未使用的资源文件**: {len(unused_resources)} 个
- **可能未使用的类文件**: {len(unused_classes)} 个
- **总计可能节省空间**: {format_size(total_size)}

⚠️ **注意**: 这是简单模式扫描，可能存在误报。请仔细检查每个文件后再决定是否删除。

## 📸 可能未使用的资源文件

"""
    
    if unused_resources:
        # 按类型分组
        resources_by_type = defaultdict(list)
        for res in unused_resources:
            resources_by_type[res['type']].append(res)
        
        for res_type, resources in resources_by_type.items():
            report += f"### {res_type.title()} ({len(resources)} 个文件)\n\n"
            report += "| 文件名 | 路径 | 大小 |\n"
            report += "|--------|------|------|\n"
            
            for res in sorted(resources, key=lambda x: x['size'], reverse=True)[:20]:  # 只显示前20个
                report += f"| {res['name']} | {res['relative_path']} | {format_size(res['size'])} |\n"
            
            if len(resources) > 20:
                report += f"| ... | ... | 还有 {len(resources) - 20} 个文件 |\n"
            
            report += "\n"
    else:
        report += "✅ 没有发现可能未使用的资源文件\n\n"
    
    report += "## 💻 可能未使用的类文件\n\n"
    
    if unused_classes:
        # 按类型分组
        classes_by_type = defaultdict(list)
        for cls in unused_classes:
            classes_by_type[cls['type']].append(cls)
        
        for cls_type, classes in classes_by_type.items():
            type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
            report += f"### {type_name} ({len(classes)} 个文件)\n\n"
            report += "| 类名 | 文件路径 | 大小 |\n"
            report += "|------|----------|------|\n"
            
            for cls in sorted(classes, key=lambda x: x['size'], reverse=True)[:20]:  # 只显示前20个
                report += f"| {cls['class_name']} | {cls['relative_path']} | {format_size(cls['size'])} |\n"
            
            if len(classes) > 20:
                report += f"| ... | ... | 还有 {len(classes) - 20} 个文件 |\n"
            
            report += "\n"
    else:
        report += "✅ 没有发现可能未使用的类文件\n\n"
    
    report += """
## ⚠️ 重要提醒

1. **这是简单模式扫描**，使用基本的字符串匹配，可能存在误报
2. **动态引用无法检测**，如通过反射、字符串拼接等方式的引用
3. **建议手动检查**每个标记为"未使用"的文件
4. **删除前备份**整个项目
5. **分批删除**并测试项目功能

## 🔧 下一步建议

1. 手动检查报告中的文件
2. 搜索项目中是否有其他引用方式
3. 确认无误后再删除文件
4. 删除后进行完整测试
"""
    
    # 保存报告
    report_file = project_path / f"simple_scan_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 报告已生成: {report_file}")

if __name__ == "__main__":
    import sys
    project_root = sys.argv[1] if len(sys.argv) > 1 else "."
    scan_android_project(project_root)
