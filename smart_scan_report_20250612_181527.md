# Android项目智能扫描报告

## 扫描信息
- **扫描时间**: 2025-06-12 18:15:27
- **项目路径**: .
- **扫描器版本**: 智能版（支持ViewBinding/DataBinding）
- **ViewBinding**: ✅ 已启用
- **DataBinding**: ✅ 已启用
- **可能未使用的资源文件**: 113 个
- **可能未使用的类文件**: 71 个
- **总计可能节省空间**: 5.0 MB

## 🔧 扫描特性

✅ **智能检测功能**:
- ✅ ViewBinding类名映射检测
- ✅ XML ID自动属性检测
- ✅ 传统R.xxx.xxx引用检测
- ✅ 类继承和接口实现检测
- ✅ AndroidManifest组件声明检测

⚠️ **已知限制**:
- 动态引用（反射、字符串拼接）无法检测
- 第三方库的隐式引用可能遗漏
- 建议手动验证标记的文件

## 📸 可能未使用的资源文件

### Other/Res (1 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| .DS_Store | .DS_Store | 6.0 KB | 🟢 安全删除 |

### Image/Mipmap-Mdpi (2 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| ic_launcher1.webp | mipmap-mdpi/ic_launcher1.webp | 2.2 KB | 🔴 应用图标 |
| ic_launcher.png | mipmap-mdpi/ic_launcher.png | 775 B | 🔴 应用图标 |

### Values (30 个文件)

⚠️ **Values文件提醒**: 可能包含多语言资源，删除前请确认

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| strings.xml | values-th-rTH/strings.xml | 279.1 KB | 🟡 谨慎删除 |
| strings.xml | values-hi-rIN/strings.xml | 278.4 KB | 🟡 谨慎删除 |
| strings.xml | values-ru-rRU/strings.xml | 243.1 KB | 🟡 谨慎删除 |
| strings.xml | values-kk-rKZ/strings.xml | 235.1 KB | 🟡 谨慎删除 |
| strings.xml | values-mn-rMN/strings.xml | 231.0 KB | 🟡 谨慎删除 |
| strings.xml | values-ar-rSA/strings.xml | 206.8 KB | 🟡 谨慎删除 |
| strings.xml | values-vi-rVN/strings.xml | 193.0 KB | 🟡 谨慎删除 |
| strings.xml | values-ja-rJP/strings.xml | 188.3 KB | 🟡 谨慎删除 |
| strings.xml | values-fr-rFR/strings.xml | 181.6 KB | 🟡 谨慎删除 |
| strings.xml | values-tl-rPH/strings.xml | 179.4 KB | 🟡 谨慎删除 |
| strings.xml | values-de-rDE/strings.xml | 178.8 KB | 🟡 谨慎删除 |
| strings.xml | values-es-rES/strings.xml | 178.1 KB | 🟡 谨慎删除 |
| strings.xml | values-pt-rPT/strings.xml | 175.4 KB | 🟡 谨慎删除 |
| strings.xml | values-it-rIT/strings.xml | 175.3 KB | 🟡 谨慎删除 |
| strings.xml | values-ko-rKR/strings.xml | 175.0 KB | 🟡 谨慎删除 |
| strings.xml | values-in-rID/strings.xml | 169.7 KB | 🟡 谨慎删除 |
| strings.xml | values-ms-rMY/strings.xml | 169.6 KB | 🟡 谨慎删除 |
| strings.xml | values/strings.xml | 163.2 KB | 🟡 谨慎删除 |
| strings.xml | values-en/strings.xml | 162.9 KB | 🟡 谨慎删除 |
| strings.xml | values-zh-rTW/strings.xml | 151.6 KB | 🟡 谨慎删除 |
| ... | ... | ... | 还有 10 个文件 |

### Animation (17 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| slide_out_left.xml | anim/slide_out_left.xml | 874 B | 🟡 需要验证 |
| slide_in_left.xml | anim/slide_in_left.xml | 874 B | 🟡 需要验证 |
| slide_out_right.xml | anim/slide_out_right.xml | 873 B | 🟡 需要验证 |
| slide_in_right.xml | anim/slide_in_right.xml | 873 B | 🟡 需要验证 |
| out.xml | anim/out.xml | 353 B | 🟡 需要验证 |
| item_fade_in.xml | anim/item_fade_in.xml | 305 B | 🟡 需要验证 |
| item_animation_fall_down.xml | anim/item_animation_fall_down.xml | 295 B | 🟡 需要验证 |
| dialog_exit_anim.xml | anim/dialog_exit_anim.xml | 259 B | 🟡 需要验证 |
| dialog_enter_anim.xml | anim/dialog_enter_anim.xml | 259 B | 🟡 需要验证 |
| slide_out_bottom.xml | anim/slide_out_bottom.xml | 224 B | 🟡 需要验证 |
| slide_in_bottom.xml | anim/slide_in_bottom.xml | 224 B | 🟡 需要验证 |
| popup_top_in.xml | anim/popup_top_in.xml | 198 B | 🟡 需要验证 |
| popup_bottom_in.xml | anim/popup_bottom_in.xml | 197 B | 🟡 需要验证 |
| popup_right_in.xml | anim/popup_right_in.xml | 197 B | 🟡 需要验证 |
| popup_top_out.xml | anim/popup_top_out.xml | 196 B | 🟡 需要验证 |
| popup_right_out.xml | anim/popup_right_out.xml | 195 B | 🟡 需要验证 |
| popup_bottom_out.xml | anim/popup_bottom_out.xml | 195 B | 🟡 需要验证 |

### Image/Mipmap-Hdpi (2 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| ic_launcher1.webp | mipmap-hdpi/ic_launcher1.webp | 3.9 KB | 🔴 应用图标 |
| ic_launcher.png | mipmap-hdpi/ic_launcher.png | 1.1 KB | 🔴 应用图标 |

### Drawable (17 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| select_price_alter_et_bg.xml | drawable/select_price_alter_et_bg.xml | 601 B | 🟡 需要验证 |
| select_rename_passkey_et_bg.xml | drawable/select_rename_passkey_et_bg.xml | 465 B | 🟡 需要验证 |
| shape_stroke_ce35728_solid_c1fe35728_r16.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r16.xml | 354 B | 🟡 需要验证 |
| shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | 354 B | 🟡 需要验证 |
| draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | 347 B | 🟡 需要验证 |
| shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | 297 B | 🟡 需要验证 |
| shape_stroke_ce35728_solid_c1fe35728_r2.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r2.xml | 297 B | 🟡 需要验证 |
| draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | 284 B | 🟡 需要验证 |
| draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | 275 B | 🟡 需要验证 |
| draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | 248 B | 🟡 需要验证 |
| draw_shape_cffffff_c1a1d20_r10.xml | drawable/draw_shape_cffffff_c1a1d20_r10.xml | 234 B | 🟡 需要验证 |
| draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | 220 B | 🟡 需要验证 |
| draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | 217 B | 🟡 需要验证 |
| draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | 214 B | 🟡 需要验证 |
| draw_shape_line_c1f1e1e1e_c1fffffff.xml | drawable/draw_shape_line_c1f1e1e1e_c1fffffff.xml | 213 B | 🟡 需要验证 |
| draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | drawable/draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | 212 B | 🟡 需要验证 |
| draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | drawable/draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | 207 B | 🟡 需要验证 |

### Image/Mipmap-Xxxhdpi (2 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| ic_launcher1.webp | mipmap-xxxhdpi/ic_launcher1.webp | 18.7 KB | 🔴 应用图标 |
| ic_launcher.png | mipmap-xxxhdpi/ic_launcher.png | 2.2 KB | 🔴 应用图标 |

### Other/Layout (1 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| .DS_Store | layout/.DS_Store | 48.0 KB | 🟢 安全删除 |

### Layout (4 个文件)

⚠️ **布局文件提醒**: 这些文件可能通过ViewBinding引用，已进行智能检测

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| footer_add_credit.xml | layout/footer_add_credit.xml | 1.1 KB | 🟡 已检测ViewBinding |
| dialog_common_list_menu.xml | layout/dialog_common_list_menu.xml | 823 B | 🟡 已检测ViewBinding |
| layout_coupon_rule.xml | layout/layout_coupon_rule.xml | 716 B | 🟡 已检测ViewBinding |
| item_coupon_manager.xml | layout/item_coupon_manager.xml | 636 B | 🟡 已检测ViewBinding |

### Image/Mipmap-Xxhdpi (2 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| ic_launcher1.webp | mipmap-xxhdpi/ic_launcher1.webp | 11.3 KB | 🔴 应用图标 |
| ic_launcher.png | mipmap-xxhdpi/ic_launcher.png | 1.9 KB | 🔴 应用图标 |

### Xml/Color (3 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| selector_new_order_point_txt_colors.xml | color/selector_new_order_point_txt_colors.xml | 410 B | 🟡 需要验证 |
| selector_kline_item_main_sub.xml | color/selector_kline_item_main_sub.xml | 270 B | 🟡 需要验证 |
| selector_login_btn_tv_color.xml | color/selector_login_btn_tv_color.xml | 269 B | 🟡 需要验证 |

### Image/Drawable-Xxhdpi (8 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| img_st_follow_empty.webp | drawable-xxhdpi/img_st_follow_empty.webp | 1.4 KB | 🟢 相对安全 |
| icon2_profile_transfer_d.webp | drawable-xxhdpi/icon2_profile_transfer_d.webp | 1.2 KB | 🟢 相对安全 |
| icon2_cb_x_circle_cf44040.webp | drawable-xxhdpi/icon2_cb_x_circle_cf44040.webp | 692 B | 🟢 相对安全 |
| img_profile_wallet_new.webp | drawable-xxhdpi/img_profile_wallet_new.webp | 590 B | 🟢 相对安全 |
| icon_source2_info_2_14x14.webp | drawable-xxhdpi/icon_source2_info_2_14x14.webp | 456 B | 🟢 相对安全 |
| icon_history.webp | drawable-xxhdpi/icon_history.webp | 442 B | 🟢 相对安全 |
| img_source_coupons.webp | drawable-xxhdpi/img_source_coupons.webp | 324 B | 🟢 相对安全 |
| img_source_right_8x6.webp | drawable-xxhdpi/img_source_right_8x6.webp | 216 B | 🟢 相对安全 |

### Xml/Xml (3 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| remote_config_defaults.xml | xml/remote_config_defaults.xml | 1.1 KB | 🟡 需要验证 |
| network_security_config.xml | xml/network_security_config.xml | 396 B | 🟡 需要验证 |
| file_paths.xml | xml/file_paths.xml | 267 B | 🟡 需要验证 |

### Image/Mipmap-Xhdpi (2 个文件)

💡 **图片文件**: 已检测传统引用和ViewBinding引用

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| ic_launcher1.webp | mipmap-xhdpi/ic_launcher1.webp | 5.8 KB | 🔴 应用图标 |
| ic_launcher.png | mipmap-xhdpi/ic_launcher.png | 1.3 KB | 🔴 应用图标 |

### Other/Font (4 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| gilroy_light.ttf | font/gilroy_light.ttf | 143.7 KB | 🟡 需要验证 |
| gilroy_medium.ttf | font/gilroy_medium.ttf | 140.4 KB | 🟡 需要验证 |
| gilroy_semi_bold.ttf | font/gilroy_semi_bold.ttf | 136.5 KB | 🟡 需要验证 |
| gilroy_bold.ttf | font/gilroy_bold.ttf | 78.8 KB | 🟡 需要验证 |

### Other/Raw (15 个文件)

| 文件名 | 路径 | 大小 | 安全等级 |
|--------|------|------|----------|
| lottie_trades_d.json | raw/lottie_trades_d.json | 12.2 KB | 🟡 需要验证 |
| lottie_trades.json | raw/lottie_trades.json | 12.1 KB | 🟡 需要验证 |
| lottie_dialog_wait.json | raw/lottie_dialog_wait.json | 10.2 KB | 🟡 需要验证 |
| lottie_orders_d.json | raw/lottie_orders_d.json | 9.3 KB | 🟡 需要验证 |
| lottie_orders.json | raw/lottie_orders.json | 9.2 KB | 🟡 需要验证 |
| lottie_discover.json | raw/lottie_discover.json | 7.0 KB | 🟡 需要验证 |
| lottie_discover_d.json | raw/lottie_discover_d.json | 7.0 KB | 🟡 需要验证 |
| lottie_dialog_alert.json | raw/lottie_dialog_alert.json | 6.8 KB | 🟡 需要验证 |
| lottie_profile.json | raw/lottie_profile.json | 6.5 KB | 🟡 需要验证 |
| lottie_profile_d.json | raw/lottie_profile_d.json | 6.4 KB | 🟡 需要验证 |
| lottie_dialog_warning.json | raw/lottie_dialog_warning.json | 6.4 KB | 🟡 需要验证 |
| lottie_dialog_error.json | raw/lottie_dialog_error.json | 5.9 KB | 🟡 需要验证 |
| lottie_promo.json | raw/lottie_promo.json | 5.7 KB | 🟡 需要验证 |
| lottie_promo_d.json | raw/lottie_promo_d.json | 5.7 KB | 🟡 需要验证 |
| lottie_dialog_ok.json | raw/lottie_dialog_ok.json | 4.9 KB | 🟡 需要验证 |

## 💻 可能未使用的类文件

### Kotlin文件 (60 个文件)

| 类名 | 文件路径 | 大小 | 类型说明 |
|------|----------|------|----------|
| DrawerTradingViewSettingDialogNew | cn/com/vau/trade/kchart/tradingview/DrawerTradingViewSettingDialogNew.kt | 16.0 KB | 视图类 |
| DynamicAdapter | cn/com/vau/util/TabLayoutExt.kt | 12.4 KB | 适配器类 |
| MyFirebaseMessagingService | cn/com/vau/common/push/MyFirebaseMessagingService.kt | 11.5 KB | 普通类 |
| LibEx | cn/com/vau/common/view/tablayout/LibEx.kt | 9.2 KB | 普通类 |
| Expand2 | cn/com/vau/util/Expand2.kt | 9.0 KB | 普通类 |
| H5DepositBean | cn/com/vau/data/depositcoupon/DepositPayDataFile.kt | 7.2 KB | 数据类 |
| BottomMonthYearDialog | cn/com/vau/util/widget/dialog/BottomMonthYearDialog.kt | 6.1 KB | 对话框类 |
| NetworkUtils | cn/com/vau/util/widget/webview/utils/NetworkUtils.kt | 5.4 KB | 工具类 |
| SelectMonthYearDialog | cn/com/vau/common/view/dialog/SelectMonthYearDialog.kt | 5.2 KB | 对话框类 |
| CouponManagerBean | cn/com/vau/data/depositcoupon/CouponDataFile.kt | 5.0 KB | 数据类 |
| BaseListBottomDialog | cn/com/vau/common/view/dialog/BaseListBottomDialog.kt | 4.7 KB | 对话框类 |
| CSConsultBean | cn/com/vau/data/msg/MsgDataFile.kt | 4.6 KB | 数据类 |
| State1 | cn/com/vau/util/LiveDataExt.kt | 3.7 KB | 普通类 |
| LiveActivePopup | cn/com/vau/signals/live/LiveActivePopup.kt | 3.5 KB | 普通类 |
| ProductItemNewsFragment | cn/com/vau/trade/fragment/kchart/ProductItemNewsFragment.kt | 3.4 KB | Fragment类 |
| BaseViewModelExt | cn/com/vau/common/mvvm/ext/BaseViewModelExt.kt | 3.2 KB | 视图类 |
| LooperDetectorUtil | cn/com/vau/util/opt/LooperDetectorUtil.kt | 3.1 KB | 工具类 |
| AttachListPopupWindow | cn/com/vau/util/widget/dialog/base/AttachListPopupWindow.kt | 3.1 KB | 普通类 |
| ImagePickerExt | cn/com/vau/util/ImagePickerExt.kt | 3.0 KB | 普通类 |
| TFADisableBeanData | cn/com/vau/data/account/AccountSettingDataFile.kt | 2.8 KB | 数据类 |
| ... | ... | ... | 还有 40 个文件 |

### Java文件 (11 个文件)

| 类名 | 文件路径 | 大小 | 类型说明 |
|------|----------|------|----------|
| addresses | cn/com/vau/common/view/InputConnectionAccomodatingLatinIMETypeNullIssues.java | 13.7 KB | 普通类 |
| SearchHistoryAdapter | cn/com/vau/trade/adapter/SearchHistoryAdapter.java | 3.5 KB | 适配器类 |
| ChartTypeRecyclerAdapter | cn/com/vau/trade/adapter/ChartTypeRecyclerAdapter.java | 3.4 KB | 适配器类 |
| PrefetchLayoutInflaterTradeItemUtil | cn/com/vau/util/PrefetchLayoutInflaterTradeItemUtil.java | 3.2 KB | 工具类 |
| PictureFileUtil | cn/com/vau/util/PictureFileUtil.java | 2.4 KB | 工具类 |
| SerializableOkHttpCookies | cn/com/vau/common/http/cookie/SerializableOkHttpCookies.java | 2.1 KB | 普通类 |
| FpsCounterUtil | cn/com/vau/util/opt/FpsCounterUtil.java | 1.5 KB | 工具类 |
| ErrorLogReportBean | cn/com/vau/common/greendao/dbUtils/ErrorLogReportBean.java | 1.3 KB | 数据类 |
| DataBoundListAdapter | cn/com/vau/common/adapter/DataBoundListAdapter.java | 1.3 KB | 适配器类 |
| BaseDialog | cn/com/vau/signals/live/history/base/BaseDialog.java | 1.0 KB | 对话框类 |
| ProductDetailsModel | cn/com/vau/trade/model/ProductDetailsModel.java | 0 B | 数据类 |

## 📊 ViewBinding映射统计

检测到 584 个布局文件的ViewBinding映射:

| 布局文件 | 生成的Binding类 | 状态 |
|----------|----------------|------|
| item_st_strategy_details_portfolio.xml | ItemStStrategyDetailsPortfolioBinding | ✅ 已检测 |
| layout_passkey_verfication.xml | LayoutPasskeyVerficationBinding | ✅ 已检测 |
| activity_open_acount_fifth_white.xml | ActivityOpenAcountFifthWhiteBinding | ✅ 已检测 |
| activity_bind_email.xml | ActivityBindEmailBinding | ✅ 已检测 |
| fragment_kline_info.xml | FragmentKlineInfoBinding | ✅ 已检测 |
| activity_open_account_first_second_white.xml | ActivityOpenAccountFirstSecondWhiteBinding | ✅ 已检测 |
| include_order_volume.xml | IncludeOrderVolumeBinding | ✅ 已检测 |
| fragment_st_strategy_details_overview.xml | FragmentStStrategyDetailsOverviewBinding | ✅ 已检测 |
| foot_recycler_viewmore.xml | FootRecyclerViewmoreBinding | ✅ 已检测 |
| activity_select_country_residence.xml | ActivitySelectCountryResidenceBinding | ✅ 已检测 |
| ... | ... | 还有 574 个映射 |


## ⚠️ 删除建议

### 🟢 相对安全（可优先删除）:
- `.DS_Store` 等系统文件
- 明显的测试或临时文件
- 重复的图片资源

### 🟡 需要谨慎（建议手动验证）:
- 布局文件（可能通过ViewBinding使用）
- 工具类和帮助类
- 自定义View和组件

### 🔴 高风险（强烈建议保留）:
- values文件夹中的多语言资源
- Application类和主要Activity
- 第三方库相关的类

## 🔧 验证步骤

1. **IDE搜索**: 在Android Studio中搜索文件名
2. **Find Usages**: 使用IDE的"Find Usages"功能
3. **全局搜索**: 搜索可能的字符串引用
4. **编译测试**: 删除后进行完整编译
5. **功能测试**: 运行应用进行功能测试

## 📝 注意事项

- 本报告基于静态分析，可能存在误报
- 删除前请务必备份项目
- 建议分批删除并逐步测试
- 如有疑问，建议保留文件
