# Android项目无用资源扫描报告

## 扫描信息
- **扫描时间**: 2025-06-12 17:59:10
- **项目路径**: /Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android
- **未使用资源文件**: 401 个
- **未使用类文件**: 145 个
- **总计可节省空间**: 6.8 MB

## 📸 未使用的资源文件

### 模块: app

#### Image/Mipmap-Mdpi (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| ic_launcher1.webp | mipmap-mdpi/ic_launcher1.webp | 2.2 KB | GGjin | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示 |

#### Values (30 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| strings.xml | values-th-rTH/strings.xml | 279.1 KB | lvyang | 2025-05-28 | [bugfix] 修改多语言问题 |
| strings.xml | values-hi-rIN/strings.xml | 278.4 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-ru-rRU/strings.xml | 243.1 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-kk-rKZ/strings.xml | 235.1 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-mn-rMN/strings.xml | 231.0 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-ar-rSA/strings.xml | 206.8 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-vi-rVN/strings.xml | 193.0 KB | lvyang | 2025-05-28 | [bugfix] 修改多语言问题 |
| strings.xml | values-ja-rJP/strings.xml | 188.3 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-fr-rFR/strings.xml | 181.6 KB | lvyang | 2025-05-28 | [bugfix] 修改多语言问题 |
| strings.xml | values-tl-rPH/strings.xml | 179.4 KB | lvyang | 2025-05-28 | [bugfix] 修改菲律宾语缺少%s占位符问题 |
| strings.xml | values-de-rDE/strings.xml | 178.8 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-es-rES/strings.xml | 178.1 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-pt-rPT/strings.xml | 175.4 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-it-rIT/strings.xml | 175.3 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-ko-rKR/strings.xml | 175.0 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-in-rID/strings.xml | 169.7 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-ms-rMY/strings.xml | 169.6 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values/strings.xml | 163.2 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-en/strings.xml | 162.9 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-zh-rTW/strings.xml | 151.6 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| strings.xml | values-zh-rCN/strings.xml | 151.5 KB | lvyang | 2025-05-27 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| styles.xml | values/styles.xml | 77.3 KB | WangJian | 2025-05-28 | [opt] style删除重复资源 |
| attrs.xml | values/attrs.xml | 32.7 KB | tianyuhe | 2025-05-26 | Merge remote-tracking branch 'origin/v3700/v3.7... |
| attr_tab_layout.xml | values/attr_tab_layout.xml | 15.7 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| dialog_styles.xml | values/dialog_styles.xml | 5.4 KB | Liam Liang | 2025-05-26 | [Dialog]弹窗间隔调整 |
| colors.xml | values/colors.xml | 3.9 KB | tianyuhe | 2025-05-26 | Merge remote-tracking branch 'origin/v3700/v3.7... |
| dimens.xml | values/dimens.xml | 2.4 KB | tianyuhe | 2025-04-17 | 【kline】k线横屏UI修改 |
| chat_style.xml | values/chat_style.xml | 337 B | felix | 2024-11-07 | [351] UI 改版，颜色整理 |
| styles.xml | values-v21/styles.xml | 330 B | darren.chen | 2021-11-14 | Fix UI |
| integers.xml | values/integers.xml | 172 B | liyang | 2022-09-02 | 提交直播 |

#### Animation (3 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| out.xml | anim/out.xml | 353 B | guangyang | 2023-05-11 | 提交资源 |
| dialog_exit_anim.xml | anim/dialog_exit_anim.xml | 259 B | Zheng | 2019-10-14 | test |
| dialog_enter_anim.xml | anim/dialog_enter_anim.xml | 259 B | Zheng | 2019-10-14 | test |

#### Image/Mipmap-Hdpi (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| ic_launcher1.webp | mipmap-hdpi/ic_launcher1.webp | 3.9 KB | GGjin | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示 |

#### Drawable (18 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| select_price_alter_et_bg.xml | drawable/select_price_alter_et_bg.xml | 601 B | felix | 2024-12-27 | [354] UI 修改 |
| select_rename_passkey_et_bg.xml | drawable/select_rename_passkey_et_bg.xml | 465 B | felix | 2024-12-27 | [354] UI 修改 |
| shape_stroke_ce35728_solid_c1fe35728_r16.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r16.xml | 354 B | GGjin | 2024-11-08 | [3.51.0] 删除无用颜色 |
| shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r16.xml | 354 B | WangJian | 2024-11-08 | [3.51.0] UI视觉改版-清理颜色色值 |
| draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml | 347 B | GGjin | 2024-11-07 | [3.51.0] 修改3d3d3d的文件 |
| shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | drawable/shape_stroke_c00c79c_solid_c1f00c79c_r2.xml | 297 B | WangJian | 2024-11-08 | [3.51.0] UI视觉改版-清理颜色色值 |
| shape_stroke_ce35728_solid_c1fe35728_r2.xml | drawable/shape_stroke_ce35728_solid_c1fe35728_r2.xml | 297 B | GGjin | 2024-11-08 | [3.51.0] 删除无用颜色 |
| draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | drawable/draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml | 284 B | GGjin | 2024-11-06 | [3.51.0] 修改bug，修改asic的图标 |
| draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml | 275 B | GGjin | 2024-11-07 | [3.51.0] 修改3d3d3d的文件 |
| draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml | 248 B | DTDT | 2024-11-05 | 351 icon2 |
| draw_shape_c1e1e1e_cebffffff_r10.xml | drawable/draw_shape_c1e1e1e_cebffffff_r10.xml | 244 B | DTDT | 2024-11-07 | 351 UI 改版 透明度:92%  ->  DB 错误修改 |
| draw_shape_cffffff_c1a1d20_r10.xml | drawable/draw_shape_cffffff_c1a1d20_r10.xml | 234 B | GGjin | 2024-11-07 | [3.51.0] 删除无用颜色 |
| draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml | 220 B | DTDT | 2024-11-04 | 351 UI 改版 clear icon source |
| draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml | 217 B | GGjin | 2024-11-02 | [3.51.0] 修改认证中心以及相关图标 |
| draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | drawable/draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml | 214 B | DTDT | 2024-11-05 | 351 icon2 |
| draw_shape_line_c1f1e1e1e_c1fffffff.xml | drawable/draw_shape_line_c1f1e1e1e_c1fffffff.xml | 213 B | GGjin | 2024-11-01 | [3.51.0] 修改设置页面以及语言、账户活动页面 |
| draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | drawable/draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml | 212 B | GGjin | 2024-11-07 | [3.51.0] 删除无用颜色 |
| draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | drawable/draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml | 207 B | GGjin | 2024-11-07 | [3.51.0] 删除无用颜色 |

#### Image/Mipmap-Xxxhdpi (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| ic_launcher1.webp | mipmap-xxxhdpi/ic_launcher1.webp | 18.7 KB | GGjin | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示 |

#### Layout (336 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| fragment_kline_chart.xml | layout/fragment_kline_chart.xml | 68.3 KB | tianyuhe | 2025-05-22 | Merge remote-tracking branch 'origin/feature_ba... |
| activity_order.xml | layout/activity_order.xml | 59.0 KB | felix | 2025-04-23 | fix |
| activity_modify_order.xml | layout/activity_modify_order.xml | 46.5 KB | felix | 2025-01-09 | [354] bugfix |
| fragment_kline_chart_new.xml | layout/fragment_kline_chart_new.xml | 40.8 KB | tianyuhe | 2025-05-26 | modify【kline】新k线与老k线UI拆分 |
| activity_st_create_and_edit_strategy.xml | layout/activity_st_create_and_edit_strategy.xml | 39.3 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_st_trades.xml | layout/fragment_st_trades.xml | 39.0 KB | WangJian | 2025-05-23 | [bugfix] 首页banner默认不展示 |
| fragment_kline_info.xml | layout/fragment_kline_info.xml | 33.5 KB | lvyang | 2024-12-02 | [3.52.0] 解决某些语言文本过长导致布局覆盖的bug |
| fragment_st_manual_trading.xml | layout/fragment_st_manual_trading.xml | 30.2 KB | array.zhou | 2025-05-07 | feat(负值清零):联调2 |
| activity_position_details.xml | layout/activity_position_details.xml | 30.1 KB | felix | 2025-02-25 | fix:统一浮动盈亏颜色 |
| fragment_order_theme.xml | layout/fragment_order_theme.xml | 30.0 KB | GGjin | 2025-05-23 | 【370】合并冲突代码 |
| activity_st_strategy_position_details.xml | layout/activity_st_strategy_position_details.xml | 24.4 KB | felix | 2025-02-08 | [356] 替换info图标 |
| activity_st_strategy_add_or_remove_funds.xml | layout/activity_st_strategy_add_or_remove_funds.xml | 24.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_open_same_name_account.xml | layout/activity_open_same_name_account.xml | 23.6 KB | lvyang | 2025-04-18 | [ACR-585] 需求：开户选择Cent账户类型增加提示（only MT4） |
| activity_open_acount_first_white.xml | layout/activity_open_acount_first_white.xml | 22.7 KB | WangJian | 2025-03-07 | [Dialog替换] AsicQuestionnaireActivity |
| activity_pending_details.xml | layout/activity_pending_details.xml | 21.5 KB | lvyang | 2024-12-13 | [3.53.0] 扩大复制订单按钮热区 |
| activity_funds.xml | layout/activity_funds.xml | 18.5 KB | array.zhou | 2025-02-20 | feat(代码整理):资金、搜索 |
| activity_manage_account.xml | layout/activity_manage_account.xml | 18.4 KB | WangJian | 2025-05-20 | [UI] UI微调 |
| activity_create_price_alert.xml | layout/activity_create_price_alert.xml | 18.4 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| activity_living_player.xml | layout/activity_living_player.xml | 18.0 KB | GGjin | 2025-01-06 | [354] 优化直播相关页面，替换RelativeLayout |
| activity_st_strategy_details.xml | layout/activity_st_strategy_details.xml | 17.5 KB | GGjin | 2025-05-23 | Merge branch 'v3700/feature_base_361_kyc' into ... |
| activity_main_new_comer_event.xml | layout/activity_main_new_comer_event.xml | 16.8 KB | WangJian | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutMa... |
| activity_open_account_first_second_white.xml | layout/activity_open_account_first_second_white.xml | 16.5 KB | GGjin | 2024-11-19 | [3.51.0] 适配阿拉伯语样式 |
| activity_security_code_settings.xml | layout/activity_security_code_settings.xml | 16.1 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| fragment_trades.xml | layout/fragment_trades.xml | 15.9 KB | WangJian | 2025-05-23 | [bugfix] 首页banner默认不展示 |
| include_order_stop_loss.xml | layout/include_order_stop_loss.xml | 15.1 KB | GGjin | 2024-11-22 | [3.52.0] 下单页面和止盈止损页面适配阿拉伯语 |
| fragment_st_copy_trading_profit_sharing.xml | layout/fragment_st_copy_trading_profit_sharing.xml | 14.8 KB | felix | 2025-02-08 | [356] 替换info图标 |
| dialog_drawer_trading_view_setting_new.xml | layout/dialog_drawer_trading_view_setting_new.xml | 14.4 KB | tianyuhe | 2025-05-16 | Merge remote-tracking branch 'origin/v3.61.0' i... |
| activity_authentication.xml | layout/activity_authentication.xml | 14.3 KB | lvyang | 2025-03-21 | Merge branch 'v3.58.0' into 358_merge_to_tablayout |
| activity_economic_calendar.xml | layout/activity_economic_calendar.xml | 14.3 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| fragment_st_academy.xml | layout/fragment_st_academy.xml | 14.3 KB | felix | 2024-12-27 | [354] UI 修改 |
| dialog_credit_transfer.xml | layout/dialog_credit_transfer.xml | 14.0 KB | GGjin | 2025-01-08 | [354] 修改部分使用weight的地方 |
| dialog_drawer_trading_view_setting.xml | layout/dialog_drawer_trading_view_setting.xml | 13.7 KB | GGjin | 2025-04-03 | [kline] 初步提交 弹窗修改 |
| fragment_st_copy_trading.xml | layout/fragment_st_copy_trading.xml | 13.4 KB | felix | 2025-03-26 | 去跟单和Live订单Tab顶部资产信息loading（产品@Jay Jiang,UI@ Mar... |
| fragment_auth_standard.xml | layout/fragment_auth_standard.xml | 13.4 KB | lvyang | 2025-04-22 | [strings] bugfix |
| dialog_reverse_order.xml | layout/dialog_reverse_order.xml | 13.4 KB | array.zhou | 2025-04-21 | feat(反向开仓\tpsl):多语言 |
| activity_funds_details.xml | layout/activity_funds_details.xml | 13.3 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| activity_st_signal_details.xml | layout/activity_st_signal_details.xml | 13.3 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_auth_plus.xml | layout/fragment_auth_plus.xml | 13.3 KB | lvyang | 2025-04-22 | [strings] bugfix |
| activity_open_acount_forth_white.xml | layout/activity_open_acount_forth_white.xml | 13.3 KB | WangJian | 2025-02-06 | [3.55.1] ASIC监管注册流程更新--应对2025年合规审查 |
| dialog_bottom_close_position.xml | layout/dialog_bottom_close_position.xml | 13.2 KB | felix | 2025-04-23 | fix |
| fragment_signal_info.xml | layout/fragment_signal_info.xml | 12.9 KB | lvyang | 2025-04-25 | [kyc] bugfix |
| fragment_tfa_link.xml | layout/fragment_tfa_link.xml | 12.3 KB | GGjin | 2024-12-02 | [3.53.0] 修改2fa添加otp页面 |
| fragment_st_strategy_orders_profit_sharing.xml | layout/fragment_st_strategy_orders_profit_sharing.xml | 12.3 KB | felix | 2025-02-08 | [356] 替换info图标 |
| fragment_st_account_fund.xml | layout/fragment_st_account_fund.xml | 11.7 KB | felix | 2025-01-06 | [3.54.0] 优化，资金相关页面，替换RelativeLayout |
| dialog_order_confirm.xml | layout/dialog_order_confirm.xml | 11.3 KB | array.zhou | 2025-02-07 | feat[dialog替换]:下单页面，确认订单弹窗 |
| activity_open_account_third_white.xml | layout/activity_open_account_third_white.xml | 11.2 KB | WangJian | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutMa... |
| activity_add_or_forgot_security_pwd.xml | layout/activity_add_or_forgot_security_pwd.xml | 11.1 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| popup_kline_lite_setting.xml | layout/popup_kline_lite_setting.xml | 11.1 KB | array.zhou | 2025-05-12 | fix(横屏kline 设置):适配阿拉伯语 |
| fragment_st_strategy_orders_settings.xml | layout/fragment_st_strategy_orders_settings.xml | 11.0 KB | lvyang | 2024-12-18 | [3.52.0 fix] 修改UI走查问题 |
| activity_trade_setting.xml | layout/activity_trade_setting.xml | 11.0 KB | tianyuhe | 2025-05-13 | feat【CTR-623】【跟单】跟单杠杆页面添加 |
| activity_choose_your_theme.xml | layout/activity_choose_your_theme.xml | 10.6 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| activity_change_login_pwd.xml | layout/activity_change_login_pwd.xml | 10.6 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| activity_st_signal_center.xml | layout/activity_st_signal_center.xml | 10.4 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_open_lv2_upload.xml | layout/fragment_open_lv2_upload.xml | 10.3 KB | WangJian | 2024-12-09 | [3.53.0] 开户上传页ui问题修复 |
| fragment_land_kline_chart_new.xml | layout/fragment_land_kline_chart_new.xml | 10.3 KB | tianyuhe | 2025-05-27 | modify【kline】新k线-横屏k线绘图工具类阿拉伯语适配 |
| item_history_position_detail_header.xml | layout/item_history_position_detail_header.xml | 10.2 KB | brin | 2025-03-26 | [language] 多语言国际化问题处理 |
| activity_help_center.xml | layout/activity_help_center.xml | 10.2 KB | WangJian | 2025-04-29 | [MVVM] HelpCenterActivity.kt |
| layout_bottom_tip_glossary.xml | layout/layout_bottom_tip_glossary.xml | 10.2 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| fragment_login_pwd_mobile.xml | layout/fragment_login_pwd_mobile.xml | 9.7 KB | lvyang | 2025-02-28 | [strings] 国家区号阿拉伯语适配 |
| layout_lottie_bottom_navigation.xml | layout/layout_lottie_bottom_navigation.xml | 9.6 KB | WangJian | 2025-02-18 | [App首页视觉优化] 首页调整 |
| activity_leverage_st.xml | layout/activity_leverage_st.xml | 9.2 KB | tianyuhe | 2025-05-13 | feat【CTR-623】【跟单】跟单杠杆页面添加 |
| activity_leverage.xml | layout/activity_leverage.xml | 9.2 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_bottom_tip_margin_level.xml | layout/layout_bottom_tip_margin_level.xml | 9.2 KB | array.zhou | 2025-02-18 | feat[dialog替换]:保证金水平 |
| layout_kline_setting_edit_view.xml | layout/layout_kline_setting_edit_view.xml | 9.1 KB | GGjin | 2025-04-19 | [kline] 添加横屏切换主副指标功能，添加修改指标值并刷新k线功能，优化指标设置弹窗切换t... |
| activity_reverse_order.xml | layout/activity_reverse_order.xml | 9.1 KB | felix | 2024-12-20 | [353] ui |
| fragment_open_lv1_acco_conf.xml | layout/fragment_open_lv1_acco_conf.xml | 8.8 KB | lvyang | 2025-04-18 | [ACR-585] 需求：开户选择Cent账户类型增加提示（only MT4） |
| dialog_close_positon_confirm.xml | layout/dialog_close_positon_confirm.xml | 8.8 KB | felix | 2025-04-23 | fix |
| fragment_login_pwd_email.xml | layout/fragment_login_pwd_email.xml | 8.7 KB | lvyang | 2025-03-26 | [358] 1、修复登录页面跳转到忘记密码页面不会携带手机号/邮箱的bug；2、多语言。 |
| activity_open_demo_guide.xml | layout/activity_open_demo_guide.xml | 8.7 KB | WangJian | 2025-02-18 | code merge |
| tp_sl_item_view.xml | layout/tp_sl_item_view.xml | 8.7 KB | array.zhou | 2025-04-22 | feat(tpsl):多语言适配 |
| activity_change_security_pwd.xml | layout/activity_change_security_pwd.xml | 8.5 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_bottom_k_line_price_change.xml | layout/dialog_bottom_k_line_price_change.xml | 8.4 KB | GGjin | 2025-02-10 | [dialog] 修改价格变化弹窗 |
| fragment_open_lv3_upload.xml | layout/fragment_open_lv3_upload.xml | 8.4 KB | lvyang | 2024-12-19 | [3.53.0] fix bug |
| activity_st_provider_to_public_trade.xml | layout/activity_st_provider_to_public_trade.xml | 8.2 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_setting.xml | layout/activity_setting.xml | 8.0 KB | GGjin | 2025-04-16 | [fix] 适配修改 h5跳转视频页面 |
| layout_kyc_welcome.xml | layout/layout_kyc_welcome.xml | 7.9 KB | GGjin | 2025-05-28 | 【kyc】修改弹窗样式，添加折行 |
| popup_land_kline_lite_settings.xml | layout/popup_land_kline_lite_settings.xml | 7.9 KB | array.zhou | 2025-05-19 | feat(横屏kline set):弹窗 |
| activity_open_account_result.xml | layout/activity_open_account_result.xml | 7.9 KB | DTDT | 2024-12-06 | 353 UI 临时调整 |
| header_recycler_order_history.xml | layout/header_recycler_order_history.xml | 7.8 KB | GGjin | 2024-11-19 | [3.51.0] 适配阿拉伯语样式 |
| activity_open_fifth_identify_example.xml | layout/activity_open_fifth_identify_example.xml | 7.8 KB | GGjin | 2024-11-06 | [3.51.0] 修改图标名字 |
| header_close_configuration.xml | layout/header_close_configuration.xml | 7.8 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| fragment_open_lv1_pers_decl.xml | layout/fragment_open_lv1_pers_decl.xml | 7.6 KB | lvyang | 2025-01-22 | [355] 多语言 |
| activity_open_fifth_address_select.xml | layout/activity_open_fifth_address_select.xml | 7.4 KB | GGjin | 2024-11-13 | [3.51.0] 修改bug |
| activity_update_mobile_number.xml | layout/activity_update_mobile_number.xml | 7.4 KB | lvyang | 2025-04-14 | [kyc] bugfix |
| activity_open_acount_fifth_white.xml | layout/activity_open_acount_fifth_white.xml | 7.3 KB | WangJian | 2024-11-18 | [3.51.0] 微调ASIC开户UI及问卷UI |
| activity_security.xml | layout/activity_security.xml | 7.3 KB | lvyang | 2025-04-14 | [kyc] 1、H5人机验证功能；2、多语言 |
| fragment_open_lv2_id_info.xml | layout/fragment_open_lv2_id_info.xml | 7.2 KB | lvyang | 2024-12-05 | [3.53.0] 多语言 |
| dialog_take_profit_stop_loss.xml | layout/dialog_take_profit_stop_loss.xml | 7.2 KB | array.zhou | 2025-04-17 | feat(tpsl):多语言走查 |
| include_order_volume.xml | layout/include_order_volume.xml | 7.1 KB | GGjin | 2024-11-15 | [3.51.0] 修改bug |
| include_fragment_trades_item_not_login.xml | layout/include_fragment_trades_item_not_login.xml | 7.1 KB | WangJian | 2025-02-21 | [App首页视觉优化] 首页UI调整 |
| activity_open_acco_guide_base.xml | layout/activity_open_acco_guide_base.xml | 7.0 KB | DTDT | 2024-11-05 | 351 icon2 |
| header_recycler_profile_top.xml | layout/header_recycler_profile_top.xml | 7.0 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| dialog_bottom_login_details.xml | layout/dialog_bottom_login_details.xml | 6.9 KB | GGjin | 2025-02-08 | [dialog] 账户选择弹窗 |
| include_order_at_price.xml | layout/include_order_at_price.xml | 6.9 KB | GGjin | 2024-11-14 | [3.51.0] 修改bug |
| activity_open_fifth_identify.xml | layout/activity_open_fifth_identify.xml | 6.8 KB | GGjin | 2024-11-18 | [3.51.0] 修改bug |
| activity_sign_up_asic.xml | layout/activity_sign_up_asic.xml | 6.7 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_login_verification.xml | layout/activity_login_verification.xml | 6.7 KB | lvyang | 2025-05-26 | [kyc] bugfix |
| activity_open_fifth_address.xml | layout/activity_open_fifth_address.xml | 6.5 KB | GGjin | 2024-11-18 | [3.51.0] 修改ui样式和颜色 |
| activity_verify_sms_code.xml | layout/activity_verify_sms_code.xml | 6.4 KB | lvyang | 2025-05-26 | [kyc] bugfix |
| fragment_sign_up_pwd.xml | layout/fragment_sign_up_pwd.xml | 6.4 KB | lvyang | 2025-05-21 | [kyc] 弹框替换 & 部分页面UI微调 |
| activity_funds_st.xml | layout/activity_funds_st.xml | 6.4 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| fragment_tfa_opt.xml | layout/fragment_tfa_opt.xml | 6.4 KB | lvyang | 2025-05-26 | [kyc] bugfix |
| activity_chart_candle_landscape.xml | layout/activity_chart_candle_landscape.xml | 6.3 KB | WangJian | 2025-02-27 | [bugfix] pro模式涨跌幅优化显示 |
| activity_input_code.xml | layout/activity_input_code.xml | 6.2 KB | lvyang | 2025-05-26 | [kyc] bugfix |
| fragment_symbol_search_search.xml | layout/fragment_symbol_search_search.xml | 6.1 KB | array.zhou | 2025-05-12 | fix(产品搜索):多语言适配2 |
| pop_calendar_order_history.xml | layout/pop_calendar_order_history.xml | 6.0 KB | felix | 2024-12-27 | [354] UI 修改 |
| activity_notice_setting.xml | layout/activity_notice_setting.xml | 6.0 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_kyc_auth_limit_view.xml | layout/layout_kyc_auth_limit_view.xml | 5.9 KB | GGjin | 2025-05-27 | 【kyc】验证中心修改样式，h5 页面修改逻辑 |
| include_order_trade_type.xml | layout/include_order_trade_type.xml | 5.9 KB | DTDT | 2024-11-22 | 352 NewOrder 顶部 sell/buy 背景图修改 |
| activity_login.xml | layout/activity_login.xml | 5.8 KB | lvyang | 2024-12-18 | [3.54.0] telegram登录 v2 |
| activity_filters.xml | layout/activity_filters.xml | 5.8 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| activity_voucher_details.xml | layout/activity_voucher_details.xml | 5.7 KB | GGjin | 2025-04-23 | [coupon] 修改coupon 刷新逻辑 |
| fragment_tfa_result.xml | layout/fragment_tfa_result.xml | 5.6 KB | lvyang | 2025-03-14 | [bugfix] 去掉2fa相关页面顶部提示文案背景圆角 |
| fragment_order_theme_st.xml | layout/fragment_order_theme_st.xml | 5.6 KB | lvyang | 2025-03-21 | Merge branch 'v3.58.0' into 358_merge_to_tablayout |
| activity_open_acount_second_white.xml | layout/activity_open_acount_second_white.xml | 5.6 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| activity_search_product.xml | layout/activity_search_product.xml | 5.5 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| fragment_open_lv3_poa_info.xml | layout/fragment_open_lv3_poa_info.xml | 5.4 KB | lvyang | 2024-11-27 | [3.52.0] 修改开户页面UI走查bug |
| activity_kline.xml | layout/activity_kline.xml | 5.4 KB | tianyuhe | 2025-05-26 | feature【kline】新老k线-新k线价格提醒按钮底部显示，老k线仍然在顶部显示 |
| fragment_tfa_bind_prompt.xml | layout/fragment_tfa_bind_prompt.xml | 5.4 KB | GGjin | 2024-11-28 | [3.52.0] 修改首次登录页面内view获取焦点导致有背景颜色的bug |
| include_fragment_trades_login_live_account.xml | layout/include_fragment_trades_login_live_account.xml | 5.4 KB | WangJian | 2025-02-24 | [App首页视觉优化] 首页UI调整 |
| popup_history_time_filter.xml | layout/popup_history_time_filter.xml | 5.3 KB | brin | 2025-03-26 | [UI] 修改部分文字的权重，添加多语言 |
| fragment_profile.xml | layout/fragment_profile.xml | 5.3 KB | GGjin | 2025-04-09 | [h5 promo] 添加活动title显示功能和交互 |
| item_other_msg.xml | layout/item_other_msg.xml | 5.2 KB | GGjin | 2024-11-07 | [3.51.0] 删除无用颜色 |
| include_fragment_trades_login.xml | layout/include_fragment_trades_login.xml | 5.1 KB | WangJian | 2025-02-21 | [App首页视觉优化] 首页UI调整 |
| activity_kyc_change_login_pwd.xml | layout/activity_kyc_change_login_pwd.xml | 5.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_sign_up_asic_pwd.xml | layout/activity_sign_up_asic_pwd.xml | 5.0 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_normal_video.xml | layout/activity_normal_video.xml | 4.9 KB | GGjin | 2025-01-06 | [354] 优化直播相关页面，替换RelativeLayout |
| header_recycler_close_history.xml | layout/header_recycler_close_history.xml | 4.9 KB | felix | 2024-12-02 | [353] 平仓历史页去掉订单方向标签 |
| fragment_open_lv1_persinfo.xml | layout/fragment_open_lv1_persinfo.xml | 4.9 KB | lvyang | 2024-11-27 | [3.52.0] 修改开户页面UI走查bug |
| activity_video_details.xml | layout/activity_video_details.xml | 4.8 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| activity_select_coupon.xml | layout/activity_select_coupon.xml | 4.7 KB | GGjin | 2025-04-14 | [coupon] 优惠券详情页面重构，删除无用优惠券页面 |
| item_system_message.xml | layout/item_system_message.xml | 4.7 KB | GGjin | 2024-11-05 | [3.51.0] 修改消息页面 |
| dialog_sumsub_agree.xml | layout/dialog_sumsub_agree.xml | 4.7 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| fragment_tfa_unbind_prompt.xml | layout/fragment_tfa_unbind_prompt.xml | 4.4 KB | lvyang | 2025-03-14 | [bugfix] 去掉2fa相关页面顶部提示文案背景圆角 |
| dialog_bottom_k_line_settings.xml | layout/dialog_bottom_k_line_settings.xml | 4.4 KB | lvyang | 2025-05-08 | [多语言] 国际化 |
| activity_select_nationality.xml | layout/activity_select_nationality.xml | 4.3 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_select_area_code_white.xml | layout/activity_select_area_code_white.xml | 4.3 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_symbol_search_category.xml | layout/fragment_symbol_search_category.xml | 4.3 KB | array.zhou | 2025-05-12 | fix(产品搜索):多语言适配2 |
| dialog_full_screen_update.xml | layout/dialog_full_screen_update.xml | 4.2 KB | GGjin | 2025-02-08 | [dialog] 替换版本更新弹窗 |
| fragment_open_trades_order.xml | layout/fragment_open_trades_order.xml | 4.2 KB | felix | 2025-03-04 | merge |
| activity_close_by_order.xml | layout/activity_close_by_order.xml | 4.2 KB | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| fragment_my_symbols.xml | layout/fragment_my_symbols.xml | 4.2 KB | WangJian | 2025-01-23 | [3.55.0] 修复添加自选列表问题 1.无自选时进入[添加自选]添加后[我的自选]未刷新问... |
| include_not_deposit_layout.xml | layout/include_not_deposit_layout.xml | 4.1 KB | WangJian | 2025-02-21 | [App首页视觉优化] 首页UI调整 |
| include_virtual_account_layout.xml | layout/include_virtual_account_layout.xml | 4.1 KB | lvyang | 2025-05-13 | [kyc] bugfix |
| activity_tfa_change.xml | layout/activity_tfa_change.xml | 4.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| header_recycler_promo.xml | layout/header_recycler_promo.xml | 4.1 KB | GGjin | 2024-12-26 | [354] 适配活动页面没有banner的情况 |
| activity_modified_close_configuration_end.xml | layout/activity_modified_close_configuration_end.xml | 4.1 KB | lvyang | 2024-11-26 | [3.52.0] fix bug |
| activity_tfa_change_prompt.xml | layout/activity_tfa_change_prompt.xml | 4.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_kyc_authentication.xml | layout/activity_kyc_authentication.xml | 4.0 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| activity_tfa_bind.xml | layout/activity_tfa_bind.xml | 4.0 KB | lvyang | 2025-04-24 | [kyc] bugfix；安全中心修改手机号增加验证旧手机号流程联调 |
| activity_verify_email_code.xml | layout/activity_verify_email_code.xml | 4.0 KB | lvyang | 2025-05-26 | [kyc] bugfix |
| activity_enter_password.xml | layout/activity_enter_password.xml | 4.0 KB | lvyang | 2025-01-06 | [3.54.0] tg登录增加crm注册账号首次登录app流程（status="8"） |
| layout_land_kline_side_controls.xml | layout/layout_land_kline_side_controls.xml | 3.9 KB | tianyuhe | 2025-04-17 | 【kline】k线横屏UI修改 |
| activity_kyc_update_phone.xml | layout/activity_kyc_update_phone.xml | 3.9 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| pop_calendar_deal_log.xml | layout/pop_calendar_deal_log.xml | 3.9 KB | GGjin | 2024-11-11 | [3.51.0] 修改bug |
| activity_sumsub_prompt.xml | layout/activity_sumsub_prompt.xml | 3.9 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| popup_top_in_app.xml | layout/popup_top_in_app.xml | 3.9 KB | lvyang | 2025-01-22 | [354 fix] InApp弹框阿拉伯语bug |
| setting_item_view.xml | layout/setting_item_view.xml | 3.9 KB | felix | 2025-02-08 | [356] 替换info图标 |
| header_recycler_signals_banner.xml | layout/header_recycler_signals_banner.xml | 3.8 KB | DTDT | 2024-12-06 | 353 UI 临时调整 |
| activity_html.xml | layout/activity_html.xml | 3.8 KB | lvyang | 2024-12-20 | [3.53.0] h5夜间模式闪白屏-还原代码 |
| fragment_tfa_reset_prompt.xml | layout/fragment_tfa_reset_prompt.xml | 3.8 KB | lvyang | 2025-03-14 | [bugfix] 去掉2fa相关页面顶部提示文案背景圆角 |
| dialog_center_input.xml | layout/dialog_center_input.xml | 3.8 KB | Liam Liang | 2025-01-08 | [Dialog]中心弹窗DismissTouchOutside调整 |
| dialog_bottom_input.xml | layout/dialog_bottom_input.xml | 3.7 KB | Liam Liang | 2025-01-22 | [Dialog封装]TextView 文字方向适配 |
| fragment_position_history.xml | layout/fragment_position_history.xml | 3.7 KB | brin | 2025-03-26 | [UI] 修改部分文字的权重，添加多语言 |
| fragment_funding_history.xml | layout/fragment_funding_history.xml | 3.7 KB | brin | 2025-03-26 | [UI] 修改部分文字的权重，添加多语言 |
| dialog_full_screen_maintenance.xml | layout/dialog_full_screen_maintenance.xml | 3.7 KB | GGjin | 2025-02-10 | [dialog] 修改维护页面弹窗名字 |
| dialog_bottom_agree_reject.xml | layout/dialog_bottom_agree_reject.xml | 3.7 KB | Liam Liang | 2025-01-22 | [Dialog封装]TextView 文字方向适配 |
| include_kyc_verify_layout.xml | layout/include_kyc_verify_layout.xml | 3.7 KB | WangJian | 2025-05-27 | [黄金开户] 首页顶部KYC栏目箭头适配阿拉伯语 |
| dialog_bottom_action.xml | layout/dialog_bottom_action.xml | 3.7 KB | Liam Liang | 2025-01-22 | [Dialog封装]TextView 文字方向适配 |
| dialog_center_action_icon.xml | layout/dialog_center_action_icon.xml | 3.6 KB | Liam Liang | 2025-05-27 | [Dialog]IconDialog Size调整 |
| activity_kyc_set_funds_pwd.xml | layout/activity_kyc_set_funds_pwd.xml | 3.6 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| view_symbol_search_item.xml | layout/view_symbol_search_item.xml | 3.6 KB | array.zhou | 2025-05-14 | feat(搜索):自选图标替换 |
| dialog_bottom_action_icon.xml | layout/dialog_bottom_action_icon.xml | 3.6 KB | Liam Liang | 2025-05-27 | [Dialog]IconDialog Size调整 |
| activity_price_alerts_manage.xml | layout/activity_price_alerts_manage.xml | 3.5 KB | lvyang | 2025-03-21 | Merge branch 'v3.58.0' into 358_merge_to_tablayout |
| dialog_auto_reject.xml | layout/dialog_auto_reject.xml | 3.5 KB | GGjin | 2025-01-08 | [354] 修改部分使用weight的地方 |
| activity_contact_us.xml | layout/activity_contact_us.xml | 3.4 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_kyc_auth_features_view.xml | layout/layout_kyc_auth_features_view.xml | 3.2 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| activity_open_account_success_asic.xml | layout/activity_open_account_success_asic.xml | 3.2 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| include_login_input_mobile.xml | layout/include_login_input_mobile.xml | 3.2 KB | lvyang | 2025-03-10 | [黄金开户] 注册页面搭建 v1 |
| activity_input_pwd.xml | layout/activity_input_pwd.xml | 3.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_sign_up.xml | layout/activity_sign_up.xml | 3.1 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_loss_order.xml | layout/activity_loss_order.xml | 3.1 KB | GGjin | 2024-11-28 | [3.52.0] 修改抹亏券订单列表页面 没有订单数据时 去掉提交按钮 |
| activity_account_error_dialog.xml | layout/activity_account_error_dialog.xml | 3.0 KB | DTDT | 2024-11-07 | Merge remote-tracking branch 'origin/master_st'... |
| activity_main.xml | layout/activity_main.xml | 3.0 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_gs_copy_balance_tip.xml | layout/layout_gs_copy_balance_tip.xml | 3.0 KB | WangJian | 2025-05-13 | [CTR-732] 跟单接入FGP和亏损补贴活动 |
| activity_personal_details.xml | layout/activity_personal_details.xml | 2.9 KB | lvyang | 2025-03-21 | Merge branch 'v3.58.0' into 358_merge_to_tablayout |
| activity_account_activity.xml | layout/activity_account_activity.xml | 2.9 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| activity_kyc_bind_phone.xml | layout/activity_kyc_bind_phone.xml | 2.9 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_kyc_update_email.xml | layout/activity_kyc_update_email.xml | 2.9 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_bottom_list.xml | layout/dialog_bottom_list.xml | 2.8 KB | GGjin | 2024-11-11 | [3.51.0] 修改bug |
| activity_edit_personal_info.xml | layout/activity_edit_personal_info.xml | 2.8 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| dialog_bottom_info_with_icon_list.xml | layout/dialog_bottom_info_with_icon_list.xml | 2.8 KB | Liam Liang | 2025-05-19 | [Dialog]添加BottomInfoWithIconListDialog类 |
| layout_gs_profit_shield_tip.xml | layout/layout_gs_profit_shield_tip.xml | 2.7 KB | WangJian | 2025-05-13 | [CTR-732] 跟单接入FGP和亏损补贴活动 |
| item_recycler_trading_view_parameters_count.xml | layout/item_recycler_trading_view_parameters_count.xml | 2.7 KB | GGjin | 2025-04-03 | [kline] 初步提交 弹窗修改 |
| view_custom_seekbar.xml | layout/view_custom_seekbar.xml | 2.7 KB | lvyang | 2024-11-21 | [3.52.0] 阿拉伯语布局适配 |
| dialog_bottom_info_list.xml | layout/dialog_bottom_info_list.xml | 2.7 KB | Liam Liang | 2025-05-19 | [Dialog]弹窗新样式修改 |
| activity_passkey_creating.xml | layout/activity_passkey_creating.xml | 2.7 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_bottom_month_year.xml | layout/dialog_bottom_month_year.xml | 2.7 KB | Liam Liang | 2024-12-23 | [Dialog]PickerViewNew组件更新 |
| header_recycler_strategy_details_portfolio.xml | layout/header_recycler_strategy_details_portfolio.xml | 2.7 KB | lvyang | 2024-12-14 | [3.53.0] 修复阿拉伯语bug（三星手机首次安装） |
| item_notices.xml | layout/item_notices.xml | 2.7 KB | GGjin | 2024-11-05 | [3.51.0] 修改消息页面 |
| activity_uploading.xml | layout/activity_uploading.xml | 2.7 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| dialog_center_action.xml | layout/dialog_center_action.xml | 2.6 KB | Liam Liang | 2025-01-08 | [Dialog]中心弹窗回调更改 |
| activity_select_country_residence.xml | layout/activity_select_country_residence.xml | 2.6 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_kyc_bind_email.xml | layout/activity_kyc_bind_email.xml | 2.6 KB | lvyang | 2025-05-26 | [kyc] 手机号和邮箱验证流程修改 & 新增2个文案 |
| include_login_input_pwd.xml | layout/include_login_input_pwd.xml | 2.6 KB | lvyang | 2025-03-10 | [黄金开户] 注册页面搭建 v1 |
| item_price_alert.xml | layout/item_price_alert.xml | 2.5 KB | GGjin | 2024-11-08 | [3.51.0] 修改分享的颜色 |
| dialog_bottom_verify.xml | layout/dialog_bottom_verify.xml | 2.5 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| fragment_calendar.xml | layout/fragment_calendar.xml | 2.5 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| dialog_bottom_community_filter.xml | layout/dialog_bottom_community_filter.xml | 2.5 KB | GGjin | 2025-02-08 | [dialog] 账户选择弹窗修改 |
| dialog_select_month_year.xml | layout/dialog_select_month_year.xml | 2.5 KB | GGjin | 2024-11-02 | [3.51.0] 修改分享弹窗样式以及弹窗底部导航条的颜色 |
| trading_view_tab_layout.xml | layout/trading_view_tab_layout.xml | 2.4 KB | GGjin | 2025-04-12 | [kline] kline指标弹窗修改 |
| activity_edit_nickname.xml | layout/activity_edit_nickname.xml | 2.4 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_et_with_clear_show.xml | layout/layout_et_with_clear_show.xml | 2.4 KB | felix | 2024-12-27 | [354] UI 修改 |
| popup_order_select.xml | layout/popup_order_select.xml | 2.4 KB | GGjin | 2024-11-07 | [3.51.0] 删除无用颜色 |
| layout_text_permission_open_account.xml | layout/layout_text_permission_open_account.xml | 2.3 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| layout_bottom_tip_copy_trading_glossary.xml | layout/layout_bottom_tip_copy_trading_glossary.xml | 2.3 KB | Felix_Han | 2025-02-17 | 增加有跟随所以无法自动清零的说明 |
| activity_disclaimer.xml | layout/activity_disclaimer.xml | 2.3 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| item_select_symbol.xml | layout/item_select_symbol.xml | 2.2 KB | brin | 2025-03-18 | Merge remote-tracking branch 'refs/remotes/orig... |
| activity_welcome.xml | layout/activity_welcome.xml | 2.2 KB | DTDT | 2024-09-05 | 350 去除基类不跟随系统字体变化代码 |
| dialog_bottom_content.xml | layout/dialog_bottom_content.xml | 2.2 KB | Liam Liang | 2025-01-22 | [Dialog封装]TextView 文字方向适配 |
| dialog_center_auto_reject.xml | layout/dialog_center_auto_reject.xml | 2.2 KB | Liam Liang | 2024-12-22 | [Dialog]方法修改 |
| activity_language.xml | layout/activity_language.xml | 2.2 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| activity_html_new.xml | layout/activity_html_new.xml | 2.2 KB | GGjin | 2025-03-31 | Merge branch 'master_st' into feature_base_356_kyc |
| fragment_free_order.xml | layout/fragment_free_order.xml | 2.2 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| fragment_tfa_password.xml | layout/fragment_tfa_password.xml | 2.1 KB | GGjin | 2024-11-19 | [3.51.0] 适配阿拉伯语样式 |
| activity_history_position_detail.xml | layout/activity_history_position_detail.xml | 2.1 KB | brin | 2025-02-28 | [feature] 适配阿拉伯语 |
| layout_edit_text_open_account.xml | layout/layout_edit_text_open_account.xml | 2.1 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| activity_modified_close_configuration.xml | layout/activity_modified_close_configuration.xml | 2.1 KB | felix | 2024-11-19 | [351] 批量平仓UI微调 |
| include_login_input_content.xml | layout/include_login_input_content.xml | 2.1 KB | lvyang | 2025-03-10 | [黄金开户] 注册页面搭建 v1 |
| fragment_discover_copy_trading.xml | layout/fragment_discover_copy_trading.xml | 2.0 KB | GGjin | 2025-05-13 | [discover] 发现页面跟单推荐tab优化 |
| popup_history_symbols_filter.xml | layout/popup_history_symbols_filter.xml | 2.0 KB | brin | 2025-03-17 | [fix] 产品选择输入框提示 |
| activity_feed_back_form.xml | layout/activity_feed_back_form.xml | 2.0 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| activity_debug_h5.xml | layout/activity_debug_h5.xml | 2.0 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| activity_bind_email.xml | layout/activity_bind_email.xml | 1.9 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_bottom_select_mt4_confirm.xml | layout/dialog_bottom_select_mt4_confirm.xml | 1.9 KB | lvyang | 2025-05-23 | [kyc] 替换dialog & 删除无用文件 |
| activity_switch_line.xml | layout/activity_switch_line.xml | 1.9 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| layout_step_open_account.xml | layout/layout_step_open_account.xml | 1.8 KB | GGjin | 2024-11-06 | [3.51.0] 修改asic提交图片页面 |
| item_credit.xml | layout/item_credit.xml | 1.8 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| activity_asic_questionnaire.xml | layout/activity_asic_questionnaire.xml | 1.8 KB | WangJian | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutMa... |
| activity_passkey_setting.xml | layout/activity_passkey_setting.xml | 1.8 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| layout_time_selection_ymd.xml | layout/layout_time_selection_ymd.xml | 1.8 KB | brin | 2025-02-28 | [feature] 时间选择器适配阿拉伯语 |
| item_tab_line_indicator_style.xml | layout/item_tab_line_indicator_style.xml | 1.8 KB | lvyang | 2025-05-15 | [bugfix] 修复夜间模式tabLayout LINE_INDICATOR 模式下小数字文... |
| activity_device_history.xml | layout/activity_device_history.xml | 1.8 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| fragment_st_search.xml | layout/fragment_st_search.xml | 1.7 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| dialog_bottom_list_root.xml | layout/dialog_bottom_list_root.xml | 1.7 KB | Liam Liang | 2025-05-19 | [Dialog]弹窗新样式修改 |
| dialog_bottom_select_list.xml | layout/dialog_bottom_select_list.xml | 1.7 KB | Liam Liang | 2025-05-19 | [Dialog]弹窗新样式修改 |
| activity_st_follow_list.xml | layout/activity_st_follow_list.xml | 1.7 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_bottom_root.xml | layout/dialog_bottom_root.xml | 1.7 KB | Liam Liang | 2025-05-19 | [Dialog]弹窗新样式修改 |
| activity_photo.xml | layout/activity_photo.xml | 1.7 KB | GGjin | 2024-11-12 | [3.51.0] 修改bug |
| view_drop_down_list.xml | layout/view_drop_down_list.xml | 1.6 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| item_tab_text_scale_style.xml | layout/item_tab_text_scale_style.xml | 1.6 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| layout_kyc_auth_requirements_view.xml | layout/layout_kyc_auth_requirements_view.xml | 1.6 KB | lvyang | 2025-04-11 | [kyc] 多语言 v1 |
| dialog_bottom_new_version_guide.xml | layout/dialog_bottom_new_version_guide.xml | 1.6 KB | GGjin | 2025-02-08 | [dialog] 替换首页新用户引导弹窗 |
| fragment_pending_order.xml | layout/fragment_pending_order.xml | 1.5 KB | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| activity_splash.xml | layout/activity_splash.xml | 1.5 KB | GGjin | 2024-11-08 | [3.51.0] 删除直播使用的特殊颜色 |
| fragment_st_strategy_orders_history.xml | layout/fragment_st_strategy_orders_history.xml | 1.5 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| view_selectable_item.xml | layout/view_selectable_item.xml | 1.5 KB | array.zhou | 2025-05-15 | feat(搜索):UI走查 |
| fragment_st_copy_trading_positions_open.xml | layout/fragment_st_copy_trading_positions_open.xml | 1.5 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| fragment_kline_pending_order.xml | layout/fragment_kline_pending_order.xml | 1.4 KB | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| fragment_add_symbols.xml | layout/fragment_add_symbols.xml | 1.4 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| layout_serial_text.xml | layout/layout_serial_text.xml | 1.4 KB | DTDT | 2024-10-23 | 351 UI全局改版 v1 |
| foot_recycler_kline_view_more.xml | layout/foot_recycler_kline_view_more.xml | 1.4 KB | GGjin | 2024-11-18 | [3.51.0] 修改bug |
| filter_introduce_dialog.xml | layout/filter_introduce_dialog.xml | 1.4 KB | WangJian | 2024-11-04 | [3.51.0] UI视觉改版 color_ca63d3d3d_c99ffffff to co... |
| dialog_bottom_k_line_new_guide.xml | layout/dialog_bottom_k_line_new_guide.xml | 1.4 KB | GGjin | 2025-02-11 | [dialog] k线设置弹窗 |
| activity_st_search.xml | layout/activity_st_search.xml | 1.4 KB | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| dialog_center_main_event.xml | layout/dialog_center_main_event.xml | 1.4 KB | Liam Liang | 2025-01-22 | [Dialog封装]增加关闭弹窗 |
| item_tab_wrap_indicator_style.xml | layout/item_tab_wrap_indicator_style.xml | 1.4 KB | lvyang | 2025-03-21 | [tabLayout] 优化代码 |
| fragment_st_copy_trading_positions_rejected.xml | layout/fragment_st_copy_trading_positions_rejected.xml | 1.4 KB | DTDT | 2024-10-24 | 351 NoDataView v2 |
| foot_recycler_deal_optional.xml | layout/foot_recycler_deal_optional.xml | 1.3 KB | WangJian | 2025-05-06 | [opt] 代码优化 |
| activity_notice.xml | layout/activity_notice.xml | 1.3 KB | lvyang | 2025-03-21 | Merge branch 'v3.58.0' into 358_merge_to_tablayout |
| fragment_st_copy_trading_positions_pending_review.xml | layout/fragment_st_copy_trading_positions_pending_review.xml | 1.3 KB | DTDT | 2024-10-24 | 351 NoDataView v2 |
| fragment_st_copy_trading_history.xml | layout/fragment_st_copy_trading_history.xml | 1.3 KB | DTDT | 2024-10-24 | 351 NoDataView v2 |
| popup_main_event_new_comer.xml | layout/popup_main_event_new_comer.xml | 1.3 KB | GGjin | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用 |
| fragment_promo.xml | layout/fragment_promo.xml | 1.3 KB | GGjin | 2025-01-17 | [355] profile页面未登录样式适配 |
| activity_st_fans_list.xml | layout/activity_st_fans_list.xml | 1.3 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_live.xml | layout/fragment_live.xml | 1.3 KB | tianyuhe | 2025-04-27 | 【VPR-4683】【modify】直播页面改MVVM |
| activity_close_history.xml | layout/activity_close_history.xml | 1.3 KB | felix | 2024-11-04 | [351] 替换缺省布局 |
| dialog_common_loading.xml | layout/dialog_common_loading.xml | 1.3 KB | array.zhou | 2025-02-18 | feat(底部弹窗):支持loading |
| activity_faqs.xml | layout/activity_faqs.xml | 1.3 KB | WangJian | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutMa... |
| foot_land_kline_tab_interval.xml | layout/foot_land_kline_tab_interval.xml | 1.3 KB | tianyuhe | 2025-04-01 | 【kline】切换时间栏逻辑处理 |
| fragment_st_community.xml | layout/fragment_st_community.xml | 1.3 KB | GGjin | 2024-08-22 | [3.49.0] ui还原度修改 |
| foot_kline_tab_interval.xml | layout/foot_kline_tab_interval.xml | 1.3 KB | WangJian | 2024-11-12 | [3.51.0] 修复K线bug |
| foot_kline_tab_interval_new.xml | layout/foot_kline_tab_interval_new.xml | 1.2 KB | tianyuhe | 2025-05-26 | modify【kline】新k线与老k线UI拆分 |
| activity_disclaimer_batch_close.xml | layout/activity_disclaimer_batch_close.xml | 1.2 KB | WangJian | 2024-11-04 | [3.51.0] UI视觉改版 color_ca63d3d3d_c99ffffff to co... |
| activity_passkey_auth_verification.xml | layout/activity_passkey_auth_verification.xml | 1.2 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| layout_coupon_progress.xml | layout/layout_coupon_progress.xml | 1.2 KB | GGjin | 2025-04-06 | [359 coupon] 修改adapter |
| fragment_fx_street.xml | layout/fragment_fx_street.xml | 1.2 KB | GGjin | 2024-10-31 | [3.51.0] 修改发现页面 |
| dialog_input_text_msg.xml | layout/dialog_input_text_msg.xml | 1.1 KB | GGjin | 2025-02-08 | [dialog] 直播输入弹窗修改，跟单首页弹窗修改 |
| fragment_st_fund_detail.xml | layout/fragment_st_fund_detail.xml | 1.1 KB | felix | 2025-01-06 | [3.54.0] 优化，资金相关页面，替换RelativeLayout |
| fragment_ss_category_symbol_list.xml | layout/fragment_ss_category_symbol_list.xml | 1.1 KB | array.zhou | 2025-04-24 | feat(搜索弹窗):代码整理 |
| footer_add_credit.xml | layout/footer_add_credit.xml | 1.1 KB | GGjin | 2024-11-05 | [3.51.0] 修改选择信用卡相关页面，删除图标 |
| activity_price_alert_list.xml | layout/activity_price_alert_list.xml | 1.1 KB | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| fragment_signals.xml | layout/fragment_signals.xml | 1.1 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| activity_tfa_setting.xml | layout/activity_tfa_setting.xml | 1.0 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| fragment_refresh_slide.xml | layout/fragment_refresh_slide.xml | 1.0 KB | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| activity_coupons_manager.xml | layout/activity_coupons_manager.xml | 1.0 KB | GGjin | 2025-04-03 | [359 coupon] 添加优惠券显示逻辑 |
| activity_histoty.xml | layout/activity_histoty.xml | 1.0 KB | lvyang | 2025-03-21 | [merge] merge code |
| activity_tfa_verify.xml | layout/activity_tfa_verify.xml | 1.0 KB | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_manage_symbols.xml | layout/activity_manage_symbols.xml | 1014 B | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| fragment_kline_orders.xml | layout/fragment_kline_orders.xml | 1003 B | felix | 2025-03-28 | merge |
| activity_edit_strategy_min.xml | layout/activity_edit_strategy_min.xml | 1003 B | lvyang | 2025-04-29 | [mvvm] 编辑跟单者准入条件 |
| activity_tfa_reset.xml | layout/activity_tfa_reset.xml | 990 B | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| dialog_center_brightness.xml | layout/dialog_center_brightness.xml | 971 B | GGjin | 2025-02-08 | [dialog] 直播弹窗修改 |
| fragment_deal_item_optional.xml | layout/fragment_deal_item_optional.xml | 967 B | WangJian | 2024-11-11 | [3.51.0] 解决首页滑动冲突 |
| fragment_deal_item_st_optional.xml | layout/fragment_deal_item_st_optional.xml | 966 B | WangJian | 2024-11-11 | [3.51.0] 解决首页滑动冲突 |
| activity_sign_up_pwd.xml | layout/activity_sign_up_pwd.xml | 947 B | lvyang | 2025-05-21 | [opt] 删除部分页面根布局background背景色，避免过渡绘制 |
| activity_config_and_unlock.xml | layout/activity_config_and_unlock.xml | 943 B | GGjin | 2025-03-20 | [fix] 修改window 默认颜色，去除部分页面使用的默认背景 |
| header_recycler_st_community_search.xml | layout/header_recycler_st_community_search.xml | 924 B | GGjin | 2024-11-06 | [3.51.0] 删除无用颜色 |
| dialog_center_volume.xml | layout/dialog_center_volume.xml | 923 B | GGjin | 2025-02-08 | [dialog] 直播弹窗修改 |
| item_popup_bottom_list_string.xml | layout/item_popup_bottom_list_string.xml | 921 B | DTDT | 2024-10-23 | 351 UI全局改版 v1 |
| view_indicator_number.xml | layout/view_indicator_number.xml | 913 B | WangJian | 2025-03-13 | 修复阿拉伯语显示问题 |
| view_selectable_grid.xml | layout/view_selectable_grid.xml | 907 B | array.zhou | 2025-04-14 | feat(kline):set 数据绑定 |
| fragment_notice.xml | layout/fragment_notice.xml | 843 B | GGjin | 2024-12-19 | [354] 完善消息优化需求 |
| fragment_ss_search_symbol_list.xml | layout/fragment_ss_search_symbol_list.xml | 837 B | array.zhou | 2025-04-24 | feat(搜索弹窗):代码整理 |
| fragment_st_signal_center_strategies.xml | layout/fragment_st_signal_center_strategies.xml | 836 B | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| fragment_st_signal_center_copier_review.xml | layout/fragment_st_signal_center_copier_review.xml | 835 B | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| dialog_common_list_menu.xml | layout/dialog_common_list_menu.xml | 823 B | Zheng | 2021-04-12 | bug |
| popup_history_funding_type_filter.xml | layout/popup_history_funding_type_filter.xml | 821 B | brin | 2025-03-05 | [UI] 添加字体适配 |
| fragment_add_symbols_item.xml | layout/fragment_add_symbols_item.xml | 813 B | lvyang | 2024-11-11 | [3.51.0] fix bug |
| item_selectable_view.xml | layout/item_selectable_view.xml | 806 B | array.zhou | 2025-04-14 | feat(kline):set 数据绑定 |
| foot_recycler_viewmore.xml | layout/foot_recycler_viewmore.xml | 759 B | WangJian | 2025-05-13 | [CTR-732] 跟单接入FGP和亏损补贴活动 |
| fragment_deal_item_st.xml | layout/fragment_deal_item_st.xml | 754 B | WangJian | 2024-11-11 | [3.51.0] 解决首页滑动冲突 |
| layout_coupon_rule.xml | layout/layout_coupon_rule.xml | 716 B | DTDT | 2024-11-09 | 351 UI 走查 bug |
| fragment_deal_item.xml | layout/fragment_deal_item.xml | 713 B | WangJian | 2024-11-11 | [3.51.0] 解决首页滑动冲突 |
| popup_attach_list.xml | layout/popup_attach_list.xml | 712 B | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| layout_kyc_dialog_content_view.xml | layout/layout_kyc_dialog_content_view.xml | 679 B | GGjin | 2025-05-23 | 【370】合并冲突代码 |
| item_coupon_manager.xml | layout/item_coupon_manager.xml | 636 B | GGjin | 2024-11-18 | [3.51.0] 修改开户相关页面的显示 |
| activity_open_upload_preview.xml | layout/activity_open_upload_preview.xml | 636 B | GGjin | 2024-11-04 | [3.51.0] 修改ui |
| fragment_st_strategy_details_portfolio.xml | layout/fragment_st_strategy_details_portfolio.xml | 604 B | lvyang | 2025-05-06 | [opt] 优化代码 |
| fragment_st_strategy_details_overview.xml | layout/fragment_st_strategy_details_overview.xml | 572 B | lvyang | 2025-03-24 | [358] 策略详情页面图表与H5联调，并处理横向滑动冲突 |
| dialog_center_seek.xml | layout/dialog_center_seek.xml | 543 B | GGjin | 2025-02-08 | [dialog] 直播弹窗修改 |
| header_recycler_search_symbols.xml | layout/header_recycler_search_symbols.xml | 460 B | GGjin | 2025-01-08 | [354] 修改StSignalSearchActivity使用心得mvvm基类 |
| layout_history_empty.xml | layout/layout_history_empty.xml | 432 B | brin | 2025-02-05 | [UI] 添加历史记录空视图 |
| fragment_st_copy_trading_positions.xml | layout/fragment_st_copy_trading_positions.xml | 418 B | lvyang | 2024-10-10 | [3.51.0] 跟单账户订单页面优化层级设计 |
| dialog_bottom_symbol_search.xml | layout/dialog_bottom_symbol_search.xml | 397 B | array.zhou | 2025-04-27 | feat(产品搜索):页面切换 |
| fragment_recyclerview.xml | layout/fragment_recyclerview.xml | 356 B | GGjin | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |

#### Other/Layout (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| .DS_Store | layout/.DS_Store | 48.0 KB | GGjin | 2024-08-06 | Updated .gitignore and removed ignored files fr... |

#### Image/Mipmap-Xxhdpi (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| ic_launcher1.webp | mipmap-xxhdpi/ic_launcher1.webp | 11.3 KB | GGjin | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示 |

#### Image/Drawable-Xxhdpi (8 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| img_st_follow_empty.webp | drawable-xxhdpi/img_st_follow_empty.webp | 1.4 KB | GGjin | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式 |
| icon2_profile_transfer_d.webp | drawable-xxhdpi/icon2_profile_transfer_d.webp | 1.2 KB | GGjin | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式 |
| icon2_cb_x_circle_cf44040.webp | drawable-xxhdpi/icon2_cb_x_circle_cf44040.webp | 692 B | GGjin | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式 |
| img_profile_wallet_new.webp | drawable-xxhdpi/img_profile_wallet_new.webp | 590 B | GGjin | 2025-01-15 | [355] 修改钱包icon的new图标 |
| icon_source2_info_2_14x14.webp | drawable-xxhdpi/icon_source2_info_2_14x14.webp | 456 B | felix | 2025-02-08 | [356] 替换info图标 |
| icon_history.webp | drawable-xxhdpi/icon_history.webp | 442 B | brin | 2025-02-26 | [feature] 历史订单详情联调 |
| img_source_coupons.webp | drawable-xxhdpi/img_source_coupons.webp | 324 B | GGjin | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式 |
| img_source_right_8x6.webp | drawable-xxhdpi/img_source_right_8x6.webp | 216 B | GGjin | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式 |

#### Image/Mipmap-Xhdpi (1 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| ic_launcher1.webp | mipmap-xhdpi/ic_launcher1.webp | 5.8 KB | GGjin | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示 |

#### 贡献者统计

| 贡献者 | 提交次数 |
|--------|----------|
| DTDT <<EMAIL>> | 3660 |
| lvyang <<EMAIL>> | 2630 |
| GGjin <<EMAIL>> | 1979 |
| WangJian <<EMAIL>> | 1537 |
| zhengjiao <xiao_0aij> | 1456 |
| Zheng <<EMAIL>> | 1024 |
| Haipeng <<EMAIL>> | 727 |
| liyang <liyang> | 668 |
| felix <<EMAIL>> | 608 |
| darren.chen <darren.chen@localhost> | 524 |
| guangyang <<EMAIL>> | 464 |
| Norman <<EMAIL>> | 375 |
| glod.guo <<EMAIL>> | 366 |
| array.zhou <<EMAIL>> | 144 |
| Norman <<EMAIL>> | 136 |
| brin <<EMAIL>> | 100 |
| tianyuhe <<EMAIL>> | 95 |
| Liam Liang <<EMAIL>> | 76 |
| GGjin <<EMAIL>> | 66 |
| wangjian <<EMAIL>> | 64 |
| WangJian <<EMAIL>> | 61 |
| GG <<EMAIL>> | 40 |
| eyal <<EMAIL>> | 32 |
| Zheng <<EMAIL>> | 31 |
| zhengjiao <<EMAIL>> | 30 |
| jasonxu <<EMAIL>> | 28 |
| wangjian <<EMAIL>> | 21 |
| flame.zheng <1234@qwer> | 20 |
| Felix_Han <<EMAIL>> | 6 |
| guang.yang <<EMAIL>> | 4 |
| lvyang <lvyang> | 2 |
| chenjs <<EMAIL>> | 1 |
| DTDT <<EMAIL>> | 1 |

## 💻 未使用的类文件

### 模块: app

#### Kotlin文件 (130 个文件)

| 类名 | 文件路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|------|----------|------|-----------|----------|----------|
| VolSeekBar | cn/com/vau/common/view/custom/VolSeekBar.kt | 22.5 KB | lvyang | 2025-02-07 | [3.55.1] firebase bugfix：ViewExt -> vibrateView... |
| StringAdapter | cn/com/vau/util/GsonUtil.kt | 18.3 KB | Liam Liang | 2025-04-23 | [kline]KlineEntity Gson解析代码优化 |
| TimeSharingChartView | cn/com/vau/trade/view/TimeSharingChartView.kt | 17.9 KB | WangJian | 2025-06-06 | [bugfix] 修复Mini分时图表 1.显示缺省布局后，收缩再展开分时图，图表闪动的问题 ... |
| Expand | cn/com/vau/util/Expand.kt | 17.1 KB | tianyuhe | 2025-05-28 | fix【kline】新老k线-浮窗涨跌幅、振幅为0时显示修改 |
| LoginPwdFragment | cn/com/vau/page/user/login/LoginPwdFragment.kt | 16.5 KB | lvyang | 2025-06-04 | [bugfix] asic邮箱登录触发V10035换设备流程，发送手机号OTP再进行登录后报账... |
| DrawerTradingViewSettingDialogNew | cn/com/vau/trade/kchart/tradingview/DrawerTradingViewSettingDialogNew.kt | 16.0 KB | array.zhou | 2025-05-19 | feat(k set):同步联动逻辑 |
| TradeGuideView | cn/com/vau/common/view/TradeGuideView.kt | 14.9 KB | WangJian | 2025-04-25 | [黄金开户] 修复bug [ACR-1166] |
| ForgetPwdSecondFragment | cn/com/vau/page/user/login/ForgetPwdSecondFragment.kt | 13.3 KB | lvyang | 2025-05-20 | [kyc] UI修改 |
| CalendarLineChart | cn/com/vau/common/view/custom/CalendarLineChart.kt | 13.3 KB | GGjin | 2024-11-29 | [3.52.0] 修改财经日历详情页面的图标的价格显示逻辑 |
| DynamicAdapter | cn/com/vau/util/TabLayoutExt.kt | 12.4 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| KLineDrawingToolbar | cn/com/vau/common/view/kchart/toolbar/KlineDrawingToolbar.kt | 11.6 KB | tianyuhe | 2025-04-21 | 【kline】横屏toolBar提升到app下，适配暗色主题 |
| TakeProfitStopLossItemView | cn/com/vau/trade/view/TakeProfitStopLossItemView.kt | 11.6 KB | GGjin | 2025-05-21 | 【kyc】ui走查修改 |
| TFAOTPFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFAOTPFragment.kt | 10.8 KB | lvyang | 2025-05-20 | [kyc] UI修改 |
| ViewExt | cn/com/vau/util/ViewExt.kt | 10.7 KB | lvyang | 2025-04-21 | [opt] view和item防双击事件修改 |
| LoginBindSecondFragment | cn/com/vau/page/user/loginBind/LoginBindSecondFragment.kt | 9.9 KB | lvyang | 2025-05-20 | [kyc] UI修改 |
| KycVerifyViewModel | cn/com/vau/util/KycVerifyHelper.kt | 9.8 KB | GGjin | 2025-05-23 | 【kyc】修改埋点 |
| RegisterFirstFragment | cn/com/vau/page/user/register/RegisterFirstFragment.kt | 9.6 KB | lvyang | 2025-05-27 | [kyc] SendCodeUtil改为非单例 & 去除注册接口返回V10010的逻辑（同步iOS） |
| LottieBottomNavigationView | cn/com/vau/common/view/LottieBottomNavigationView.kt | 9.3 KB | WangJian | 2025-02-18 | [App首页视觉优化] 首页调整 |
| LibEx | cn/com/vau/common/view/tablayout/LibEx.kt | 9.2 KB | lvyang | 2025-03-08 | [tabLayout] 替换掉系统tabLayout |
| Expand2 | cn/com/vau/util/Expand2.kt | 9.0 KB | eyal | 2025-05-26 | Expand2用ThreadLocal |
| ForgetPwdThirdFragment | cn/com/vau/page/user/login/ForgetPwdThirdFragment.kt | 8.5 KB | lvyang | 2025-05-15 | [opt] 接口方法名称后面增加“Api” |
| TimeSelectionYmd | cn/com/vau/history/ui/widget/TimeSelectionYmd.kt | 7.6 KB | brin.xue | 2025-05-06 | [bugfix] 禁止时间选择器中 “天”的数据循环重排 |
| SymbolSearchItemView | cn/com/vau/trade/view/SymbolSearchItemView.kt | 7.2 KB | array.zhou | 2025-05-16 | fix(产品搜索):连续点击无法关闭弹窗 |
| H5DepositBean | cn/com/vau/data/depositcoupon/DepositPayDataFile.kt | 7.2 KB | felix | 2024-11-12 | [352] 入金接入中台 |
| LoginBindFragment | cn/com/vau/page/user/loginBind/LoginBindFragment.kt | 7.1 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| ScrollTextView | cn/com/vau/common/view/ScrollTextView.kt | 6.7 KB | WangJian | 2025-02-20 | [App首页视觉优化] 首页UI调整 |
| LandKLineControlsSideView | cn/com/vau/page/setting/activity/LandKLineControlsSideView.kt | 6.5 KB | tianyuhe | 2025-05-27 | opt【kline】新k线-保存是否显示绘图，并且不同KlineActivity进行同步是否显示绘图 |
| RegisterFirstSecondFragment | cn/com/vau/page/user/register/RegisterFirstSecondFragment.kt | 6.3 KB | lvyang | 2025-05-27 | [kyc] SendCodeUtil改为非单例 & 去除注册接口返回V10010的逻辑（同步iOS） |
| BottomMonthYearDialog | cn/com/vau/util/widget/dialog/BottomMonthYearDialog.kt | 6.1 KB | brin | 2025-01-16 | [feature]  历史持仓的详情页 |
| SettingItemView | cn/com/vau/common/view/custom/SettingItemView.kt | 6.0 KB | GGjin | 2025-03-11 | [kyc] 初步修改安全页面 |
| SelectableGridView | cn/com/vau/trade/view/SelectableGridView.kt | 5.9 KB | array.zhou | 2025-04-16 | feat(kline):set 代码整理2 |
| TFAResultFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFAResultFragment.kt | 5.8 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| KlineSettingEditView | cn/com/vau/util/widget/KlineSettingEditView.kt | 5.5 KB | GGjin | 2025-04-19 | [kline] 添加横屏切换主副指标功能，添加修改指标值并刷新k线功能，优化指标设置弹窗切换t... |
| NetworkUtils | cn/com/vau/util/widget/webview/utils/NetworkUtils.kt | 5.4 KB | Liam Liang | 2025-01-13 | [WebView]预加载、WebViewPool提供WebView加载速度 |
| SelectMonthYearDialog | cn/com/vau/common/view/dialog/SelectMonthYearDialog.kt | 5.2 KB | GGjin | 2024-08-20 | [3.49.0] 合并时间格式化工具类 |
| SumSubJumpViewModel | cn/com/vau/page/user/sumsub/SumSubJumpHelper.kt | 5.2 KB | GGjin | 2025-03-29 | [kyc] 修改原因的sumsub跳转逻辑，添加kyc需求的验证和跳转逻辑 |
| DragLayout | cn/com/vau/common/view/kchart/toolbar/DragLayout.kt | 5.1 KB | tianyuhe | 2025-05-16 | modify【kline】保存k线绘图工具栏设置的画笔颜色、粗细、位置 |
| AppBarLayoutBehavior | cn/com/vau/common/view/AppBarLayoutBehavior.kt | 5.1 KB | GGjin | 2024-08-20 | [3.49.0] 修改工具类 |
| CustomSeekBar | cn/com/vau/common/view/CustomSeekBar.kt | 5.0 KB | GGjin | 2024-09-21 | [3.51.0] 修改工具类 |
| CouponManagerBean | cn/com/vau/data/depositcoupon/CouponDataFile.kt | 5.0 KB | tianyuhe | 2025-05-23 | delete【跟单杠杆】重复接口删除 |
| LoginInputMobileView | cn/com/vau/common/view/login/LoginInputMobileView.kt | 4.7 KB | lvyang | 2025-03-24 | [kyc] 包名修改 & 优化代码 |
| BaseListBottomDialog | cn/com/vau/common/view/dialog/BaseListBottomDialog.kt | 4.7 KB | GGjin | 2025-05-03 | 【discover】修改使用LinearLayoutManager的地方 |
| CSConsultBean | cn/com/vau/data/msg/MsgDataFile.kt | 4.6 KB | GGjin | 2025-03-13 | [fix] 修改faq页面 |
| StringExt | cn/com/vau/util/StringExt.kt | 4.3 KB | brin | 2025-01-24 | [feature] 完成列表中的说明弹窗 |
| BannerIndicatorView | cn/com/vau/common/view/custom/BannerIndicatorView.kt | 4.3 KB | DTDT | 2024-10-25 | 351 bug & ui & icon命名 |
| LoginInputPwdView | cn/com/vau/common/view/login/LoginInputPwdView.kt | 4.2 KB | lvyang | 2025-03-24 | [kyc] 包名修改 & 优化代码 |
| TFABindPromptFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFABindPromptFragment.kt | 4.2 KB | GGjin | 2025-03-25 | [h5] 修改认证中心 银行电汇的跳转链接 |
| LoginInputContentView | cn/com/vau/common/view/login/LoginInputContentView.kt | 4.0 KB | lvyang | 2025-03-28 | [kyc] 安全中心关联和认证修改邮箱；优化部分逻辑。 |
| OpenAccountPermissionText | cn/com/vau/common/view/OpenAccountPermissionText.kt | 4.0 KB | DTDT | 2024-11-06 | 351 UI 改版 ARGB 色值 & bug |
| DashedTextView | cn/com/vau/util/widget/DashedTextView.kt | 3.9 KB | felix | 2025-05-14 | fix:多语言问题 |
| StepOpenAccountView | cn/com/vau/common/view/StepOpenAccountView.kt | 3.8 KB | GGjin | 2024-11-06 | [3.51.0] 修改asic提交图片页面 |
| State1 | cn/com/vau/util/LiveDataExt.kt | 3.7 KB | brin | 2025-01-24 | [feature] 合并多个弹窗的定义和监听 |
| NestedScrollableHost | cn/com/vau/common/view/NestedScrollableHost.kt | 3.7 KB | DTDT | 2024-09-18 | 350 order ViewPager2 |
| OpenAccountDropDown | cn/com/vau/common/view/OpenAccountDropDown.kt | 3.6 KB | lvyang | 2025-01-21 | [354 fix] 修复阿拉伯语bug |
| TFAVerifyFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFAVerifyFragment.kt | 3.5 KB | lvyang | 2025-05-15 | [opt] 接口方法名称后面增加“Api” |
| LiveActivePopup | cn/com/vau/signals/live/LiveActivePopup.kt | 3.5 KB | GGjin | 2025-02-24 | [bugfix] 修改sumsub跳转逻辑 |
| ProductItemNewsFragment | cn/com/vau/trade/fragment/kchart/ProductItemNewsFragment.kt | 3.4 KB | WangJian | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutMa... |
| PositionPopupWindow | cn/com/vau/util/widget/dialog/base/PositionPopupWindow.kt | 3.3 KB | Liam Liang | 2025-03-05 | [Dialog封装]Android13,返回键适配 |
| BaseViewModelExt | cn/com/vau/common/mvvm/ext/BaseViewModelExt.kt | 3.2 KB | lvyang | 2025-04-02 | [kyc] 安全中心邮箱和手机号接口联调 v1 |
| CurrencyFormatEditText | cn/com/vau/common/view/CurrencyFormatEditText.kt | 3.2 KB | GGjin | 2024-08-23 | [firebase] 修改线上bug 0823 |
| LooperDetectorUtil | cn/com/vau/util/opt/LooperDetectorUtil.kt | 3.1 KB | eyal | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用 |
| AttachListPopupWindow | cn/com/vau/util/widget/dialog/base/AttachListPopupWindow.kt | 3.1 KB | Liam Liang | 2024-12-16 | Android弹窗统一封装 |
| OpenAccountForActivityResult | cn/com/vau/common/view/OpenAccountForActivityResult.kt | 3.0 KB | DTDT | 2024-11-04 | 351 UI改版 icon2 |
| ImagePickerExt | cn/com/vau/util/ImagePickerExt.kt | 3.0 KB | GGjin | 2025-04-01 | [fix] 修改launch方式，兼容mix2 |
| HeadlessFragment | cn/com/vau/util/PermissionUtil.kt | 3.0 KB | GGjin | 2025-04-01 | [kyc] 修改h5交互逻辑 |
| OpenAccountDatePicker | cn/com/vau/common/view/OpenAccountDatePicker.kt | 2.9 KB | DTDT | 2024-11-04 | 351 UI改版 icon2 |
| OpenAccountEditText | cn/com/vau/common/view/OpenAccountEditText.kt | 2.9 KB | felix | 2024-12-27 | [354] UI 修改 |
| UserInfo | cn/com/vau/common/base/UserInfo.kt | 2.8 KB | WangJian | 2024-09-05 | [3.49.0] [优化] 替换用户数据库 v1 |
| TFADisableBeanData | cn/com/vau/data/account/AccountSettingDataFile.kt | 2.8 KB | WangJian | 2025-05-19 | 合并master_st至黄金开户分支 |
| WebViewNestedScrollView | cn/com/vau/common/view/custom/WebViewNestedScrollView.kt | 2.6 KB | lvyang | 2025-03-24 | [358] 策略详情页面图表与H5联调，并处理横向滑动冲突 |
| LinkSpanTextView | cn/com/vau/common/view/system/LinkSpanTextView.kt | 2.5 KB | WangJian | 2025-05-13 | [CTR-732] 跟单接入FGP和亏损补贴活动 |
| ColorPickerView | cn/com/vau/common/view/kchart/toolbar/ColorPickerView.kt | 2.4 KB | tianyuhe | 2025-04-21 | 【kline】横屏toolBar提升到app下，适配暗色主题 |
| TradingViewTabLayout | cn/com/vau/trade/kchart/tradingview/TradingViewTabSelector.kt | 2.4 KB | GGjin | 2024-12-11 | [3.53.0] ui还原度修改 |
| LinePickerView | cn/com/vau/common/view/kchart/toolbar/LinePickerView.kt | 2.3 KB | tianyuhe | 2025-04-21 | 【kline】横屏toolBar提升到app下，适配暗色主题 |
| PortraitIndicatorSettingPopup | cn/com/vau/trade/kchart/pop/PortraitIndicatorSettingPopup.kt | 2.2 KB | GGjin | 2024-06-07 | 348  修改行情页不dismiss的bug，修改线上bug。 |
| TFALinkFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFALinkFragment.kt | 2.2 KB | lvyang | 2025-05-15 | [opt] 接口方法名称后面增加“Api” |
| TransferDealBean | cn/com/vau/data/depositcoupon/TransferDataFile.kt | 2.2 KB | GGjin | 2025-04-14 | [coupon] 优惠券详情页面重构，删除无用优惠券页面 |
| Dist | cn/com/vau/util/Dist.kt | 2.2 KB | WangJian | 2025-02-20 | [App首页视觉优化] 首页UI调整 |
| ViewExtensions | cn/com/vau/signals/live/ViewExtensions.kt | 2.1 KB | DTDT | 2024-08-12 | 349 string & param_bundle |
| DashedUnderlinedTextView | cn/com/vau/history/ui/widget/DashedUnderlineTextView.kt | 2.1 KB | brin | 2025-02-28 | [feature] 时间选择器适配阿拉伯语 |
| MaxLimitRecyclerView | cn/com/vau/common/view/system/MaxLimitRecyclerView.kt | 2.0 KB | WangJian | 2024-05-28 | K线页订单弹窗限制最大展示4条 |
| RefreshSlideLayout | cn/com/vau/common/view/RefreshSlideLayout.kt | 1.9 KB | GGjin | 2024-05-14 | 348 策略列表页面滑动优化 |
| ProductListAdapter | cn/com/vau/trade/adapter/ProductListAdapter.kt | 1.8 KB | GGjin | 2024-11-14 | [3.51.0] 修改bug |
| BindingAdapterExt | cn/com/vau/common/adapter/BindingAdapterExt.kt | 1.7 KB | array.zhou | 2025-01-24 | feat(adapter):BindingAdapter，避免创建Adapter类和ViewH... |
| DefaultAdapter | cn/com/vau/util/widget/DefaultAdapter.kt | 1.7 KB | GGjin | 2025-03-17 | [kyc] 修改kyc认证中心 |
| MarketDiffItemCallback | cn/com/vau/trade/adapter/MarketDiffItemCallback.kt | 1.7 KB | DTDT | 2024-07-23 | 349 init v2 |
| RecyclerViewBehavior | cn/com/vau/common/view/RecyclerViewBehavior.kt | 1.7 KB | GGjin | 2024-08-20 | [3.49.0] 修改工具类 |
| TFAPwdFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFAPwdFragment.kt | 1.7 KB | lvyang | 2025-05-15 | [opt] 接口方法名称后面增加“Api” |
| StShareOrderData | cn/com/vau/data/init/StShareOrderData.kt | 1.6 KB | DTDT | 2024-09-26 | 351 合并适配器：多品牌挂单 - 跟单自主挂单 |
| StSignalInvestedAdapter | cn/com/vau/signals/stsignal/adapter/StSignalInvestedAdapter.kt | 1.6 KB | GGjin | 2025-03-24 | [fix] 修改部分文件夹名，删除入金相关页面 |
| TFAResetPromptFragment | cn/com/vau/profile/activity/twoFactorAuth/fragment/TFAResetPromptFragment.kt | 1.5 KB | lvyang | 2025-04-17 | [kyc] bugfix |
| CameraBottomPop | cn/com/vau/common/view/popup/CameraBottomPop.kt | 1.4 KB | DTDT | 2024-09-14 | 350 view |
| OrderPendingDiffItemCallback | cn/com/vau/trade/adapter/OrderPendingDiffItemCallback.kt | 1.4 KB | DTDT | 2024-07-24 | 349 init v4 |
| MineType | cn/com/vau/util/widget/webview/offline/type/MineType.kt | 1.4 KB | Liam Liang | 2025-02-26 | [WebView优化]URL预加载,js、css、字体等资源内置 |
| IndicatorNumView | cn/com/vau/common/view/custom/IndicatorNumView.kt | 1.4 KB | WangJian | 2025-03-13 | 修复阿拉伯语显示问题 |
| SelectableItemView | cn/com/vau/trade/view/SelectableItemView.kt | 1.3 KB | array.zhou | 2025-04-14 | feat(kline):set 数据绑定 |
| TypeValueExt | cn/com/vau/util/TypeValueExt.kt | 1.3 KB | brin.xue | 2025-05-12 | Merge branch 'release_3.58.0_bugfix' into v3.61.0 |
| DepositBundleData | cn/com/vau/page/DepositBundleData.kt | 1.2 KB | GGjin | 2025-02-27 | [picture] 选择图片方式添加压缩流程，并使用uri进行上传换传参 |
| TestElapsedTimeUtil | cn/com/vau/util/opt/TestElapsedTimeUtil.kt | 1.1 KB | eyal | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用 |
| InterceptEventRecyclerView | cn/com/vau/trade/kchart/InterceptEventRecyclerView.kt | 1.1 KB | felix | 2025-04-21 | fix:滑动冲突 |
| StrictModeUtil | cn/com/vau/util/opt/StrictModeUtil.kt | 991 B | eyal | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用 |
| OpenAccountSecondContract | cn/com/vau/page/user/openAccountSecond/OpenAccountSecondContract.kt | 910 B | DTDT | 2024-08-24 | 349 data |
| OpenConditionBean | cn/com/vau/data/trade/TradeInfoSettingDataFile.kt | 903 B | tianyuhe | 2025-05-13 | feat【CTR-623】【跟单】跟单杠杆页面添加 |
| NewsLetterData | cn/com/vau/data/discover/NewsLetterDataFile.kt | 896 B | GGjin | 2024-11-01 | [3.51.0] 合并类似adapter 优化逻辑 |
| LineHeightSpanDefault | cn/com/vau/util/SpannableStringBuilder.kt | 890 B | brin | 2025-01-24 | [feature] 完成列表中的说明弹窗 |
| ScanCreditData | cn/com/vau/page/ScanCreditData.kt | 824 B | GGjin | 2025-02-27 | [picture] 选择图片方式添加压缩流程，并使用uri进行上传换传参 |
| EditTextVerifyComponent | cn/com/vau/common/view/EditTextVerifyComponent.kt | 810 B | WangJian | 2024-04-26 | 优化验证组件 v1.1 1.解耦VerifyComponent与自定义组件，让其各负其责 2.... |
| BundleExt | cn/com/vau/util/BundleExt.kt | 741 B | array.zhou | 2025-04-22 | feat(产品搜索):tab页面 |
| ApiServiceHelper | cn/com/vau/common/http/ApiServiceHelper.kt | 733 B | GGjin | 2025-02-15 | [http] 从NetWorkService拆分 非交易 交易 以及跟单接口 为不同对象请求 |
| VauBridgeWebView | cn/com/vau/common/view/VauBridgeWebView.kt | 717 B | GGjin | 2024-08-20 | [3.49.0] 合并时间格式化工具类 |
| OpenAcountForthModel | cn/com/vau/page/user/openAccountForth/OpenAcountForthModel.kt | 697 B | Zheng | 2021-04-12 | bug |
| LogExt | cn/com/vau/util/opt/LogExt.kt | 672 B | GGjin | 2025-01-07 | [354] 修改bug,修改直接调用log的方法 |
| KLinePriceChangeBar | cn/com/vau/common/view/KLinePriceChangeBar.kt | 622 B | eyal | 2024-10-22 | 优化代码 |
| AppEventsLoggerInitializer | cn/com/vau/common/utils/initializer/AppEventsLoggerInitializer.kt | 575 B | Zheng | 2021-12-09 | bug |
| BasePopupViewExt | cn/com/vau/util/widget/dialog/base/BasePopupViewExt.kt | 555 B | Liam Liang | 2025-02-19 | [Dialog]弹窗遮盖问题修改 |
| LayoutParamsExt | cn/com/vau/util/LayoutParamsExt.kt | 523 B | eyal | 2025-05-14 | HeaderBar在动态显示返回按钮的情况下调整了边距 |
| AuthRepository | cn/com/vau/profile/activity/authentication/AuthRepository.kt | 494 B | WangJian | 2024-09-14 | [3.51.0] [优化] 替换用户数据库 |
| ArrowBottomAdapter | cn/com/vau/common/view/popup/adapter/ArrowBottomAdapter.kt | 489 B | GGjin | 2024-04-02 | 创建策略添加弹窗和逻辑 |
| HistoryRequestBean | cn/com/vau/trade/bean/HistoryRequestBean.kt | 462 B | DTDT | 2023-07-04 | 331 |
| SquareFrameLayout | cn/com/vau/util/widget/SquareFrameLayout.kt | 446 B | felix | 2025-01-06 | [354] 替换RelativeLayout |
| TmpRecordUtil | cn/com/vau/util/TmpRecordUtil.kt | 396 B | eyal | 2025-05-28 | 为了查找NaN问题，上报为不严重类型 |
| KLineEvent2 | cn/com/vau/trade/bean/KLineEvent2.kt | 368 B | tianyuhe | 2025-04-10 | 【kline】k线竖屏与简洁横屏无需同步来自TradingView的LineExtra的设置 |
| SelectDataBean | cn/com/vau/signals/stsignal/SelectDataBean.kt | 226 B | GGjin | 2025-03-24 | [fix] 修改部分文件夹名，删除入金相关页面 |
| BaseBindingPerformance | cn/com/vau/common/performance/BaseBindingPerformance.kt | 175 B | array.zhou | 2025-01-13 | build(performance):功能拆分 |
| CalendarFiltersEvent | cn/com/vau/page/CalendarFiltersEvent.kt | 154 B | DTDT | 2024-08-12 | 349 string & param_bundle |
| SetStopLossTakeProfitActivity | cn/com/vau/trade/activity/SetStopLossTakeProfitActivity.kt | 0 B | felix | 2025-04-24 | merge |
| ProductListFragment | cn/com/vau/trade/fragment/order/ProductListFragment.kt | 0 B | GGjin | 2025-05-13 | Merge branch 'feature_base_360_discover' into v... |
| KOpenTradesFragment | cn/com/vau/trade/fragment/kchart/KOpenTradesFragment.kt | 0 B | array.zhou | 2025-04-25 | Merge remote-tracking branch 'refs/remotes/orig... |
| StKOpenTradesFragment | cn/com/vau/trade/fragment/kchart/StKOpenTradesFragment.kt | 0 B | array.zhou | 2025-04-25 | Merge remote-tracking branch 'refs/remotes/orig... |
| StSetStopLossTakeProfitActivity | cn/com/vau/trade/st/activity/StSetStopLossTakeProfitActivity.kt | 0 B | felix | 2025-04-24 | merge |

#### Java文件 (15 个文件)

| 类名 | 文件路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|------|----------|------|-----------|----------|----------|
| addresses | cn/com/vau/common/view/InputConnectionAccomodatingLatinIMETypeNullIssues.java | 13.7 KB | Norman | 2022-12-22 | 修复PasswordView 和 注册第三步的问题 |
| PickerViewNew | cn/com/vau/common/view/timeSelection/PickerViewNew.java | 12.8 KB | Liam Liang | 2024-12-23 | [Dialog]PickerViewNew组件更新 |
| VFXOrderChart | cn/com/vau/common/view/custom/VFXOrderChart.java | 11.6 KB | DTDT | 2024-11-07 | 351 UI 改版 clear color |
| ViewStubPro | cn/com/vau/history/ui/widget/ViewStubPro.java | 4.3 KB | brin | 2025-02-06 | [UI] 添加历史记录错误状态以及 Loading 状态 |
| SearchHistoryAdapter | cn/com/vau/trade/adapter/SearchHistoryAdapter.java | 3.5 KB | GGjin | 2025-03-27 | [fix] 去除冗余逻辑代码 |
| ChartTypeRecyclerAdapter | cn/com/vau/trade/adapter/ChartTypeRecyclerAdapter.java | 3.4 KB | lvyang | 2024-12-07 | [3.53.0] Language相关迁移SpManager |
| PrefetchLayoutInflaterTradeItemUtil | cn/com/vau/util/PrefetchLayoutInflaterTradeItemUtil.java | 3.2 KB | eyal | 2024-09-07 | AB开关接入，列表分段刷新接入 |
| HeartLayout | cn/com/vau/signals/live/heart/HeartLayout.java | 2.4 KB | GGjin | 2025-01-06 | [354] 优化直播相关页面，替换RelativeLayout |
| PictureFileUtil | cn/com/vau/util/PictureFileUtil.java | 2.4 KB | GGjin | 2025-02-19 | [picture] 去除图片选择库，使用系统的方式获取图片和拍照，涉及页面，上传信用卡、lv2... |
| SerializableOkHttpCookies | cn/com/vau/common/http/cookie/SerializableOkHttpCookies.java | 2.1 KB | Zheng | 2019-10-14 | test |
| FpsCounterUtil | cn/com/vau/util/opt/FpsCounterUtil.java | 1.5 KB | eyal | 2024-11-25 | 注掉了生产消费Log日志 |
| ErrorLogReportBean | cn/com/vau/common/greendao/dbUtils/ErrorLogReportBean.java | 1.3 KB | Haipeng | 2019-12-11 | 错误日志上传工具类 |
| DataBoundListAdapter | cn/com/vau/common/adapter/DataBoundListAdapter.java | 1.3 KB | array.zhou | 2025-01-24 | feat(adapter):BindingAdapter，避免创建Adapter类和ViewH... |
| BaseDialog | cn/com/vau/signals/live/history/base/BaseDialog.java | 1.0 KB | DTDT | 2024-10-25 | 351 ui & icon命名 |
| ProductDetailsModel | cn/com/vau/trade/model/ProductDetailsModel.java | 0 B | felix | 2024-12-16 | merge |

#### 贡献者统计

| 贡献者 | 提交次数 |
|--------|----------|
| GGjin <<EMAIL>> | 467 |
| DTDT <<EMAIL>> | 363 |
| lvyang <<EMAIL>> | 274 |
| WangJian <<EMAIL>> | 213 |
| Zheng <<EMAIL>> | 77 |
| felix <<EMAIL>> | 70 |
| zhengjiao <xiao_0aij> | 70 |
| Haipeng <<EMAIL>> | 50 |
| eyal <<EMAIL>> | 42 |
| array.zhou <<EMAIL>> | 40 |
| guangyang <<EMAIL>> | 30 |
| Norman <<EMAIL>> | 27 |
| tianyuhe <<EMAIL>> | 26 |
| brin <<EMAIL>> | 25 |
| wangjian <<EMAIL>> | 22 |
| liyang <liyang> | 20 |
| Liam Liang <<EMAIL>> | 16 |
| darren.chen <darren.chen@localhost> | 13 |
| WangJian <<EMAIL>> | 10 |
| Zheng <<EMAIL>> | 8 |
| chenjs <<EMAIL>> | 8 |
| GGjin <<EMAIL>> | 6 |
| flame.zheng <1234@qwer> | 5 |
| wangjian <<EMAIL>> | 5 |
| glod.guo <<EMAIL>> | 4 |
| Norman <<EMAIL>> | 4 |
| brin.xue <<EMAIL>> | 3 |
| jasonxu <<EMAIL>> | 3 |
| GG <<EMAIL>> | 3 |
| WangJian <821124love> | 1 |

## 🔧 清理建议

### 资源文件清理
1. **谨慎删除**: 在删除资源文件前，请仔细检查是否有动态引用（如通过字符串拼接的资源名）
2. **备份项目**: 建议在清理前创建项目备份
3. **分批清理**: 建议分批删除并测试，避免一次性删除过多文件
4. **检查第三方库**: 某些资源可能被第三方库使用，删除前请确认

### 类文件清理
1. **检查反射调用**: 某些类可能通过反射调用，静态分析无法检测到
2. **检查配置文件**: 类可能在配置文件或注解中被引用
3. **检查测试代码**: 确认类没有在测试代码中使用
4. **检查动态加载**: 某些类可能通过动态加载使用

### 自动化清理脚本
可以使用以下命令批量删除未使用的文件（请谨慎使用）：

```bash
# 删除未使用的资源文件（示例）
# 请根据实际情况修改路径

# 删除未使用的资源文件
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/res/mipmap-mdpi/ic_launcher1.webp"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/res/values-v21/styles.xml"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/res/values-es-rES/strings.xml"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/res/anim/out.xml"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/res/anim/dialog_exit_anim.xml"
# ... 还有 396 个文件

# 删除未使用的类文件
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/java/cn/com/vau/page/ScanCreditData.kt"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/java/cn/com/vau/page/DepositBundleData.kt"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/java/cn/com/vau/page/CalendarFiltersEvent.kt"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/java/cn/com/vau/util/LayoutParamsExt.kt"
# rm "/Users/<USER>/Downloads/Code/Hytech/Version/0002/au-android/app/src/main/java/cn/com/vau/util/TmpRecordUtil.kt"
# ... 还有 140 个文件
```

⚠️ **警告**: 请在删除前仔细检查每个文件，确保不会影响项目正常运行！
