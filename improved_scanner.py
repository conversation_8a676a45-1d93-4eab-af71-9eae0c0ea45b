#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源改进扫描器
支持ViewBinding、DataBinding等现代Android开发模式
"""

import os
import re
import json
import time
from pathlib import Path
from typing import Set, Dict, List
from collections import defaultdict

class ImprovedAndroidScanner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.app_src_main = self.project_root / "app" / "src" / "main"
        self.java_src = self.app_src_main / "java"
        self.res_dir = self.app_src_main / "res"
        
        # 存储结果
        self.unused_resources = []
        self.unused_classes = []
        
        # 缓存所有文件内容
        self.file_contents = {}
        
        # ViewBinding和DataBinding生成的类名映射
        self.binding_mappings = {}
        
    def scan_all(self):
        """执行改进扫描"""
        print("🚀 改进扫描器启动...")
        print(f"📁 项目根目录: {self.project_root}")
        
        # 检查是否启用了ViewBinding/DataBinding
        self._check_binding_features()
        
        # 预加载所有文件内容
        print("📖 预加载文件内容...")
        self._preload_files()
        
        # 生成ViewBinding/DataBinding映射
        print("🔗 生成Binding映射...")
        self._generate_binding_mappings()
        
        # 扫描资源文件
        print("📸 扫描资源文件...")
        self._scan_resources()
        
        # 扫描代码文件
        print("💻 扫描代码文件...")
        self._scan_code_files()
        
        # 生成报告
        print("📊 生成报告...")
        self._generate_report()
        
        print("✅ 扫描完成！")
    
    def _check_binding_features(self):
        """检查项目是否启用了ViewBinding/DataBinding"""
        build_gradle = self.project_root / "app" / "build.gradle"
        build_gradle_kts = self.project_root / "app" / "build.gradle.kts"
        
        build_file = build_gradle if build_gradle.exists() else build_gradle_kts
        if not build_file.exists():
            print("⚠️ 未找到build.gradle文件")
            return
        
        try:
            with open(build_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'viewBinding' in content and 'true' in content:
                print("✅ 检测到ViewBinding已启用")
            if 'dataBinding' in content and 'true' in content:
                print("✅ 检测到DataBinding已启用")
                
        except Exception as e:
            print(f"⚠️ 读取build.gradle失败: {e}")
    
    def _preload_files(self):
        """预加载所有需要检查的文件内容"""
        files_to_load = []
        
        # Java/Kotlin文件
        if self.java_src.exists():
            files_to_load.extend(self.java_src.rglob("*.java"))
            files_to_load.extend(self.java_src.rglob("*.kt"))
        
        # XML文件
        if self.res_dir.exists():
            files_to_load.extend(self.res_dir.rglob("*.xml"))
        
        # AndroidManifest.xml
        manifest = self.app_src_main / "AndroidManifest.xml"
        if manifest.exists():
            files_to_load.append(manifest)
        
        total_files = len(files_to_load)
        print(f"   需要加载 {total_files} 个文件")
        
        for i, file_path in enumerate(files_to_load):
            if i % 100 == 0:
                progress = (i / total_files) * 100
                print(f"   加载进度: {i}/{total_files} ({progress:.1f}%)")
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    self.file_contents[str(file_path)] = f.read()
            except Exception:
                self.file_contents[str(file_path)] = ""
        
        print(f"   ✅ 完成加载 {len(self.file_contents)} 个文件")
    
    def _generate_binding_mappings(self):
        """生成ViewBinding/DataBinding的类名映射"""
        if not self.res_dir.exists():
            return
        
        layout_files = list(self.res_dir.glob("layout*/*.xml"))
        print(f"   找到 {len(layout_files)} 个布局文件")
        
        for layout_file in layout_files:
            layout_name = layout_file.stem
            # 转换为ViewBinding类名: activity_main -> ActivityMainBinding
            binding_class = self._layout_to_binding_class(layout_name)
            self.binding_mappings[layout_name] = binding_class
            
        print(f"   生成了 {len(self.binding_mappings)} 个Binding映射")
    
    def _layout_to_binding_class(self, layout_name: str) -> str:
        """将布局文件名转换为ViewBinding类名"""
        # activity_main -> ActivityMainBinding
        parts = layout_name.split('_')
        camel_case = ''.join(word.capitalize() for word in parts)
        return f"{camel_case}Binding"
    
    def _scan_resources(self):
        """扫描资源文件"""
        if not self.res_dir.exists():
            print("❌ 资源目录不存在")
            return
        
        resource_files = []
        for res_subdir in self.res_dir.iterdir():
            if res_subdir.is_dir():
                for file_path in res_subdir.rglob("*"):
                    if file_path.is_file():
                        resource_files.append(file_path)
        
        total_resources = len(resource_files)
        print(f"   找到 {total_resources} 个资源文件")
        
        # 分析每个资源文件
        for i, file_path in enumerate(resource_files):
            if i % 50 == 0:
                progress = (i / total_resources) * 100
                print(f"   扫描进度: {i}/{total_resources} ({progress:.1f}%)")
            
            resource_name = file_path.stem
            is_used = self._check_resource_usage(file_path, resource_name)
            
            if not is_used:
                self.unused_resources.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'relative_path': str(file_path.relative_to(self.res_dir)),
                    'size': file_path.stat().st_size,
                    'type': self._get_resource_type(file_path)
                })
        
        print(f"   ✅ 发现 {len(self.unused_resources)} 个可能未使用的资源文件")
    
    def _check_resource_usage(self, file_path: Path, resource_name: str) -> bool:
        """检查资源文件是否被使用（支持ViewBinding/DataBinding）"""
        full_name = file_path.name
        
        # 1. 检查传统引用方式
        traditional_patterns = [
            rf'R\.[\w]+\.{re.escape(resource_name)}\b',
            rf'@[\w]+/{re.escape(resource_name)}\b',
            rf'"{re.escape(resource_name)}"',
            rf"'{re.escape(resource_name)}'",
            rf'{re.escape(full_name)}'
        ]
        
        # 2. 检查ViewBinding/DataBinding引用
        binding_patterns = []
        if file_path.parent.name.startswith('layout'):
            # 布局文件可能通过ViewBinding引用
            binding_class = self.binding_mappings.get(resource_name, "")
            if binding_class:
                binding_patterns.extend([
                    rf'{re.escape(binding_class)}\b',
                    rf'databinding\.{re.escape(binding_class)}\b',
                    rf'binding\.{re.escape(binding_class)}\b'
                ])
        
        # 3. 检查ID引用（ViewBinding会为每个ID生成属性）
        id_patterns = []
        if file_path.suffix == '.xml':
            content = self.file_contents.get(str(file_path), "")
            # 提取XML中的所有ID
            id_matches = re.findall(r'android:id="@\+id/(\w+)"', content)
            for id_name in id_matches:
                id_patterns.extend([
                    rf'\.{re.escape(id_name)}\b',  # binding.idName
                    rf'R\.id\.{re.escape(id_name)}\b'  # R.id.idName
                ])
        
        all_patterns = traditional_patterns + binding_patterns + id_patterns
        
        # 在所有文件内容中搜索
        for content in self.file_contents.values():
            for pattern in all_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return True
        
        return False
    
    def _scan_code_files(self):
        """扫描代码文件"""
        if not self.java_src.exists():
            print("❌ Java源码目录不存在")
            return
        
        code_files = []
        for file_path in self.java_src.rglob("*"):
            if file_path.is_file() and file_path.suffix in {'.java', '.kt'}:
                code_files.append(file_path)
        
        total_files = len(code_files)
        print(f"   找到 {total_files} 个代码文件")
        
        # 分析每个代码文件
        for i, file_path in enumerate(code_files):
            if i % 50 == 0:
                progress = (i / total_files) * 100
                print(f"   扫描进度: {i}/{total_files} ({progress:.1f}%)")
            
            class_name = self._extract_class_name(file_path)
            is_used = self._check_class_usage(file_path, class_name)
            
            if not is_used:
                self.unused_classes.append({
                    'name': file_path.name,
                    'class_name': class_name,
                    'path': str(file_path),
                    'relative_path': str(file_path.relative_to(self.java_src)),
                    'size': file_path.stat().st_size,
                    'type': file_path.suffix
                })
        
        print(f"   ✅ 发现 {len(self.unused_classes)} 个可能未使用的类文件")
    
    def _check_class_usage(self, file_path: Path, class_name: str) -> bool:
        """检查类文件是否被使用"""
        patterns = [
            rf'\b{re.escape(class_name)}\b',
            rf'import\s+.*\.{re.escape(class_name)}\b',
            rf'android:name=".*{re.escape(class_name)}"',
            rf'class.*:\s*{re.escape(class_name)}\b',  # Kotlin继承
            rf'extends\s+{re.escape(class_name)}\b'    # Java继承
        ]
        
        # 在所有文件内容中搜索引用（排除自身）
        for file_path_str, content in self.file_contents.items():
            if file_path_str == str(file_path):
                continue
            
            for pattern in patterns:
                if re.search(pattern, content):
                    return True
        
        return False
    
    def _extract_class_name(self, file_path: Path) -> str:
        """提取类名"""
        content = self.file_contents.get(str(file_path), "")
        if content:
            match = re.search(r'(?:public\s+)?(?:class|interface|enum|object)\s+(\w+)', content)
            if match:
                return match.group(1)
        return file_path.stem
    
    def _get_resource_type(self, file_path: Path) -> str:
        """获取资源类型"""
        parent_dir = file_path.parent.name
        extension = file_path.suffix.lower()
        
        if extension in {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}:
            return f"image/{parent_dir}"
        elif extension == '.xml':
            if parent_dir.startswith('layout'):
                return "layout"
            elif parent_dir.startswith('drawable'):
                return "drawable"
            elif parent_dir.startswith('values'):
                return "values"
            elif parent_dir.startswith('anim'):
                return "animation"
            else:
                return f"xml/{parent_dir}"
        else:
            return f"other/{parent_dir}"

    def _generate_report(self):
        """生成报告"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        total_size = sum(r['size'] for r in self.unused_resources) + \
                    sum(c['size'] for c in self.unused_classes)

        # 生成Markdown报告
        report = f"""# Android项目无用资源扫描报告（改进版）

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {self.project_root}
- **扫描器版本**: 改进版（支持ViewBinding/DataBinding）
- **可能未使用的资源文件**: {len(self.unused_resources)} 个
- **可能未使用的类文件**: {len(self.unused_classes)} 个
- **总计可能节省空间**: {self._format_size(total_size)}

## 🔧 扫描特性

✅ **支持现代Android开发模式**:
- ViewBinding引用检测
- DataBinding引用检测
- XML ID自动生成属性检测
- 传统R.xxx.xxx引用检测

⚠️ **注意事项**:
- 这是静态分析，可能存在误报
- 动态引用（反射、字符串拼接）无法检测
- 建议手动验证每个标记的文件

## 📸 可能未使用的资源文件

"""

        if self.unused_resources:
            # 按类型分组
            resources_by_type = defaultdict(list)
            for res in self.unused_resources:
                resources_by_type[res['type']].append(res)

            for res_type, resources in resources_by_type.items():
                report += f"### {res_type.title()} ({len(resources)} 个文件)\n\n"
                report += "| 文件名 | 路径 | 大小 | 检查说明 |\n"
                report += "|--------|------|------|----------|\n"

                for res in sorted(resources, key=lambda x: x['size'], reverse=True)[:15]:
                    check_note = self._get_check_note(res)
                    report += f"| {res['name']} | {res['relative_path']} | {self._format_size(res['size'])} | {check_note} |\n"

                if len(resources) > 15:
                    report += f"| ... | ... | ... | 还有 {len(resources) - 15} 个文件 |\n"

                report += "\n"
        else:
            report += "✅ 没有发现可能未使用的资源文件\n\n"

        report += "## 💻 可能未使用的类文件\n\n"

        if self.unused_classes:
            # 按类型分组
            classes_by_type = defaultdict(list)
            for cls in self.unused_classes:
                classes_by_type[cls['type']].append(cls)

            for cls_type, classes in classes_by_type.items():
                type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
                report += f"### {type_name} ({len(classes)} 个文件)\n\n"
                report += "| 类名 | 文件路径 | 大小 | 检查说明 |\n"
                report += "|------|----------|------|----------|\n"

                for cls in sorted(classes, key=lambda x: x['size'], reverse=True)[:15]:
                    check_note = "已检查继承、接口、注解等引用"
                    report += f"| {cls['class_name']} | {cls['relative_path']} | {self._format_size(cls['size'])} | {check_note} |\n"

                if len(classes) > 15:
                    report += f"| ... | ... | ... | 还有 {len(classes) - 15} 个文件 |\n"

                report += "\n"
        else:
            report += "✅ 没有发现可能未使用的类文件\n\n"

        report += f"""
## 📊 ViewBinding/DataBinding 映射统计

生成了 {len(self.binding_mappings)} 个布局文件的Binding映射:

"""

        if self.binding_mappings:
            report += "| 布局文件 | 生成的Binding类 |\n"
            report += "|----------|----------------|\n"
            for layout, binding in list(self.binding_mappings.items())[:10]:
                report += f"| {layout}.xml | {binding} |\n"
            if len(self.binding_mappings) > 10:
                report += f"| ... | 还有 {len(self.binding_mappings) - 10} 个映射 |\n"

        report += """

## ⚠️ 重要提醒

### 删除前请确认:
1. **手动检查每个文件** - 静态分析可能遗漏某些引用
2. **检查动态引用** - 反射、字符串拼接等方式的引用
3. **检查第三方库** - 某些资源可能被第三方库使用
4. **检查测试代码** - 确认测试代码中没有引用
5. **备份项目** - 删除前务必备份整个项目

### 建议的验证方法:
1. 在IDE中搜索文件名
2. 使用"Find Usages"功能
3. 检查是否有字符串形式的引用
4. 分批删除并测试

## 🔧 下一步建议

1. 优先删除明显的垃圾文件（如.DS_Store）
2. 谨慎处理布局文件（可能通过ViewBinding引用）
3. 手动验证每个标记的类文件
4. 删除后进行完整的编译和测试
"""

        # 保存报告
        report_file = self.project_root / f"improved_scan_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📄 报告已生成: {report_file}")

        # 生成JSON报告
        json_data = {
            "scan_info": {
                "timestamp": timestamp,
                "project_path": str(self.project_root),
                "scanner_version": "improved",
                "total_unused_resources": len(self.unused_resources),
                "total_unused_classes": len(self.unused_classes),
                "total_size_bytes": total_size,
                "binding_mappings_count": len(self.binding_mappings)
            },
            "unused_resources": self.unused_resources,
            "unused_classes": self.unused_classes,
            "binding_mappings": self.binding_mappings
        }

        json_file = self.project_root / f"improved_scan_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

        print(f"📄 JSON报告: {json_file}")

    def _get_check_note(self, resource_info: dict) -> str:
        """获取资源检查说明"""
        file_name = resource_info['name']
        res_type = resource_info['type']

        if file_name == '.DS_Store':
            return "系统文件，可安全删除"
        elif res_type == "layout":
            return "已检查ViewBinding引用"
        elif res_type.startswith("image/"):
            return "已检查传统和Binding引用"
        elif res_type == "values":
            return "可能是多语言文件，谨慎删除"
        else:
            return "已检查常规引用"

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """主函数"""
    import sys

    project_path = sys.argv[1] if len(sys.argv) > 1 else "."
    project_path = Path(project_path).resolve()

    if not project_path.exists():
        print(f"❌ 项目路径不存在: {project_path}")
        return 1

    scanner = ImprovedAndroidScanner(str(project_path))
    scanner.scan_all()
    return 0


if __name__ == "__main__":
    exit(main())
