#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源快速扫描器 - 优化版本
"""

import os
import re
import json
import time
from pathlib import Path
from typing import Set, Dict, List
from collections import defaultdict

class QuickAndroidScanner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.app_src_main = self.project_root / "app" / "src" / "main"
        self.java_src = self.app_src_main / "java"
        self.res_dir = self.app_src_main / "res"
        
        # 存储结果
        self.unused_resources = []
        self.unused_classes = []
        
        # 缓存所有文件内容，避免重复读取
        self.file_contents = {}
        
    def scan_all(self):
        """执行快速扫描"""
        print("🚀 快速扫描模式启动...")
        print(f"📁 项目根目录: {self.project_root}")
        
        # 预加载所有文件内容
        print("📖 预加载文件内容...")
        self._preload_files()
        
        # 扫描资源文件
        print("📸 扫描资源文件...")
        self._scan_resources()
        
        # 扫描代码文件
        print("💻 扫描代码文件...")
        self._scan_code_files()
        
        # 生成报告
        print("📊 生成报告...")
        self._generate_report()
        
        print("✅ 扫描完成！")
    
    def _preload_files(self):
        """预加载所有需要检查的文件内容"""
        files_to_load = []
        
        # Java/Kotlin文件
        if self.java_src.exists():
            files_to_load.extend(self.java_src.rglob("*.java"))
            files_to_load.extend(self.java_src.rglob("*.kt"))
        
        # XML文件
        if self.res_dir.exists():
            files_to_load.extend(self.res_dir.rglob("*.xml"))
        
        # AndroidManifest.xml
        manifest = self.app_src_main / "AndroidManifest.xml"
        if manifest.exists():
            files_to_load.append(manifest)
        
        print(f"   加载 {len(files_to_load)} 个文件...")
        
        for file_path in files_to_load:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    self.file_contents[str(file_path)] = f.read()
            except Exception:
                self.file_contents[str(file_path)] = ""
    
    def _scan_resources(self):
        """扫描资源文件"""
        if not self.res_dir.exists():
            print("❌ 资源目录不存在")
            return
        
        resource_files = []
        for res_subdir in self.res_dir.iterdir():
            if res_subdir.is_dir():
                for file_path in res_subdir.rglob("*"):
                    if file_path.is_file():
                        resource_files.append(file_path)
        
        print(f"   找到 {len(resource_files)} 个资源文件")
        
        # 分析每个资源文件
        for i, file_path in enumerate(resource_files):
            if i % 100 == 0:
                print(f"   处理进度: {i}/{len(resource_files)}")
            
            resource_name = file_path.stem
            is_used = False
            
            # 在所有文件内容中搜索引用
            for content in self.file_contents.values():
                if self._check_resource_reference(content, resource_name, file_path.name):
                    is_used = True
                    break
            
            if not is_used:
                self.unused_resources.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'relative_path': str(file_path.relative_to(self.res_dir)),
                    'size': file_path.stat().st_size,
                    'type': self._get_resource_type(file_path)
                })
    
    def _scan_code_files(self):
        """扫描代码文件"""
        if not self.java_src.exists():
            print("❌ Java源码目录不存在")
            return
        
        code_files = []
        for file_path in self.java_src.rglob("*"):
            if file_path.is_file() and file_path.suffix in {'.java', '.kt'}:
                code_files.append(file_path)
        
        print(f"   找到 {len(code_files)} 个代码文件")
        
        # 分析每个代码文件
        for i, file_path in enumerate(code_files):
            if i % 100 == 0:
                print(f"   处理进度: {i}/{len(code_files)}")
            
            class_name = self._extract_class_name(file_path)
            is_used = False
            
            # 在所有文件内容中搜索引用（排除自身）
            for file_path_str, content in self.file_contents.items():
                if file_path_str == str(file_path):
                    continue
                
                if self._check_class_reference(content, class_name):
                    is_used = True
                    break
            
            if not is_used:
                self.unused_classes.append({
                    'name': file_path.name,
                    'class_name': class_name,
                    'path': str(file_path),
                    'relative_path': str(file_path.relative_to(self.java_src)),
                    'size': file_path.stat().st_size,
                    'type': file_path.suffix
                })
    
    def _check_resource_reference(self, content: str, resource_name: str, full_name: str) -> bool:
        """检查资源引用"""
        patterns = [
            rf'R\.[\w]+\.{re.escape(resource_name)}\b',
            rf'@[\w]+/{re.escape(resource_name)}\b',
            rf'"{re.escape(resource_name)}"',
            rf"'{re.escape(resource_name)}'",
            rf'{re.escape(full_name)}'
        ]
        
        for pattern in patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    def _check_class_reference(self, content: str, class_name: str) -> bool:
        """检查类引用"""
        patterns = [
            rf'\b{re.escape(class_name)}\b',
            rf'import\s+.*\.{re.escape(class_name)}\b',
            rf'android:name=".*{re.escape(class_name)}"'
        ]
        
        for pattern in patterns:
            if re.search(pattern, content):
                return True
        return False
    
    def _extract_class_name(self, file_path: Path) -> str:
        """提取类名"""
        content = self.file_contents.get(str(file_path), "")
        if content:
            match = re.search(r'(?:public\s+)?(?:class|interface|enum)\s+(\w+)', content)
            if match:
                return match.group(1)
        return file_path.stem
    
    def _get_resource_type(self, file_path: Path) -> str:
        """获取资源类型"""
        parent_dir = file_path.parent.name
        extension = file_path.suffix.lower()
        
        if extension in {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}:
            return f"image/{parent_dir}"
        elif extension == '.xml':
            if parent_dir.startswith('layout'):
                return "layout"
            elif parent_dir.startswith('drawable'):
                return "drawable"
            elif parent_dir.startswith('values'):
                return "values"
            elif parent_dir.startswith('anim'):
                return "animation"
            else:
                return f"xml/{parent_dir}"
        else:
            return f"other/{parent_dir}"
    
    def _generate_report(self):
        """生成报告"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        total_size = sum(r['size'] for r in self.unused_resources) + \
                    sum(c['size'] for c in self.unused_classes)
        
        # 生成Markdown报告
        report = f"""# Android项目无用资源扫描报告（快速模式）

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {self.project_root}
- **未使用资源文件**: {len(self.unused_resources)} 个
- **未使用类文件**: {len(self.unused_classes)} 个
- **总计可节省空间**: {self._format_size(total_size)}

## 📸 未使用的资源文件

"""
        
        if self.unused_resources:
            # 按类型分组
            resources_by_type = defaultdict(list)
            for res in self.unused_resources:
                resources_by_type[res['type']].append(res)
            
            for res_type, resources in resources_by_type.items():
                report += f"### {res_type.title()} ({len(resources)} 个文件)\n\n"
                report += "| 文件名 | 路径 | 大小 |\n"
                report += "|--------|------|------|\n"
                
                for res in sorted(resources, key=lambda x: x['size'], reverse=True):
                    report += f"| {res['name']} | {res['relative_path']} | {self._format_size(res['size'])} |\n"
                
                report += "\n"
        else:
            report += "✅ 没有发现未使用的资源文件\n\n"
        
        report += "## 💻 未使用的类文件\n\n"
        
        if self.unused_classes:
            # 按类型分组
            classes_by_type = defaultdict(list)
            for cls in self.unused_classes:
                classes_by_type[cls['type']].append(cls)
            
            for cls_type, classes in classes_by_type.items():
                type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
                report += f"### {type_name} ({len(classes)} 个文件)\n\n"
                report += "| 类名 | 文件路径 | 大小 |\n"
                report += "|------|----------|------|\n"
                
                for cls in sorted(classes, key=lambda x: x['size'], reverse=True):
                    report += f"| {cls['class_name']} | {cls['relative_path']} | {self._format_size(cls['size'])} |\n"
                
                report += "\n"
        else:
            report += "✅ 没有发现未使用的类文件\n\n"
        
        # 保存报告
        report_file = self.project_root / f"quick_scan_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 报告已生成: {report_file}")
        
        # 生成JSON报告
        json_data = {
            "scan_info": {
                "timestamp": timestamp,
                "project_path": str(self.project_root),
                "total_unused_resources": len(self.unused_resources),
                "total_unused_classes": len(self.unused_classes),
                "total_size_bytes": total_size
            },
            "unused_resources": self.unused_resources,
            "unused_classes": self.unused_classes
        }
        
        json_file = self.project_root / f"quick_scan_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 JSON报告: {json_file}")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """主函数"""
    import sys
    
    project_path = sys.argv[1] if len(sys.argv) > 1 else "."
    project_path = Path(project_path).resolve()
    
    if not project_path.exists():
        print(f"❌ 项目路径不存在: {project_path}")
        return 1
    
    scanner = QuickAndroidScanner(str(project_path))
    scanner.scan_all()
    return 0


if __name__ == "__main__":
    exit(main())
