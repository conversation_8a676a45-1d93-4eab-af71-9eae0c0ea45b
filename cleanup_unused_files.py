#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用文件清理工具
根据扫描报告自动清理无用的资源和类文件
"""

import os
import json
import shutil
from pathlib import Path
from typing import List, Dict

class UnusedFilesCleanup:
    def __init__(self, report_file: str):
        self.report_file = Path(report_file)
        self.backup_dir = None
        self.deleted_files = []
        
    def load_report(self) -> Dict:
        """加载扫描报告"""
        if not self.report_file.exists():
            raise FileNotFoundError(f"报告文件不存在: {self.report_file}")
        
        with open(self.report_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def create_backup(self, project_root: str) -> str:
        """创建备份目录"""
        import time
        backup_name = f"backup_{time.strftime('%Y%m%d_%H%M%S')}"
        self.backup_dir = Path(project_root) / backup_name
        self.backup_dir.mkdir(exist_ok=True)
        print(f"📦 创建备份目录: {self.backup_dir}")
        return str(self.backup_dir)
    
    def backup_file(self, file_path: str) -> str:
        """备份单个文件"""
        if not self.backup_dir:
            raise ValueError("备份目录未创建")
        
        source_path = Path(file_path)
        if not source_path.exists():
            return None
        
        # 保持相对路径结构
        relative_path = source_path.name
        backup_path = self.backup_dir / relative_path
        
        # 如果文件名冲突，添加序号
        counter = 1
        original_backup_path = backup_path
        while backup_path.exists():
            stem = original_backup_path.stem
            suffix = original_backup_path.suffix
            backup_path = self.backup_dir / f"{stem}_{counter}{suffix}"
            counter += 1
        
        shutil.copy2(source_path, backup_path)
        return str(backup_path)
    
    def delete_files(self, files_info: List[Dict], file_type: str, 
                    create_backup: bool = True) -> int:
        """删除文件列表"""
        if not files_info:
            print(f"✅ 没有需要删除的{file_type}")
            return 0
        
        print(f"\n🗑️ 准备删除 {len(files_info)} 个{file_type}:")
        
        deleted_count = 0
        for file_info in files_info:
            file_path = Path(file_info['path'])
            
            if not file_path.exists():
                print(f"⚠️ 文件不存在，跳过: {file_path}")
                continue
            
            try:
                # 创建备份
                if create_backup:
                    backup_path = self.backup_file(str(file_path))
                    if backup_path:
                        print(f"📦 备份: {file_path.name} -> {backup_path}")
                
                # 删除文件
                file_path.unlink()
                print(f"🗑️ 删除: {file_path}")
                
                self.deleted_files.append({
                    'path': str(file_path),
                    'type': file_type,
                    'size': file_info.get('size', 0),
                    'backup_path': backup_path if create_backup else None
                })
                
                deleted_count += 1
                
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")
        
        return deleted_count
    
    def cleanup(self, create_backup: bool = True, 
               delete_resources: bool = True, 
               delete_classes: bool = True) -> Dict:
        """执行清理操作"""
        report_data = self.load_report()
        project_root = report_data['scan_info']['project_path']
        
        print(f"🧹 开始清理项目: {project_root}")
        
        # 创建备份
        if create_backup:
            self.create_backup(project_root)
        
        results = {
            'deleted_resources': 0,
            'deleted_classes': 0,
            'total_deleted': 0,
            'saved_size': 0
        }
        
        # 删除资源文件
        if delete_resources:
            unused_resources = report_data.get('unused_resources', [])
            results['deleted_resources'] = self.delete_files(
                unused_resources, "资源文件", create_backup
            )
        
        # 删除类文件
        if delete_classes:
            unused_classes = report_data.get('unused_classes', [])
            results['deleted_classes'] = self.delete_files(
                unused_classes, "类文件", create_backup
            )
        
        # 计算总计
        results['total_deleted'] = results['deleted_resources'] + results['deleted_classes']
        results['saved_size'] = sum(f['size'] for f in self.deleted_files)
        
        return results
    
    def generate_cleanup_report(self, results: Dict) -> str:
        """生成清理报告"""
        import time
        
        report = f"""# 文件清理报告

## 清理信息
- **清理时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **源报告**: {self.report_file}
- **备份目录**: {self.backup_dir or '未创建备份'}

## 清理结果
- **删除的资源文件**: {results['deleted_resources']} 个
- **删除的类文件**: {results['deleted_classes']} 个
- **总计删除**: {results['total_deleted']} 个文件
- **节省空间**: {self._format_size(results['saved_size'])}

## 删除的文件列表

"""
        
        if self.deleted_files:
            # 按类型分组
            resources = [f for f in self.deleted_files if f['type'] == '资源文件']
            classes = [f for f in self.deleted_files if f['type'] == '类文件']
            
            if resources:
                report += "### 删除的资源文件\n\n"
                for file_info in resources:
                    report += f"- {Path(file_info['path']).name} ({self._format_size(file_info['size'])})\n"
                report += "\n"
            
            if classes:
                report += "### 删除的类文件\n\n"
                for file_info in classes:
                    report += f"- {Path(file_info['path']).name} ({self._format_size(file_info['size'])})\n"
                report += "\n"
        
        if self.backup_dir:
            report += f"""## 恢复说明

如果需要恢复删除的文件，可以从备份目录恢复：
```bash
# 备份目录: {self.backup_dir}
# 请手动复制需要恢复的文件到原位置
```

"""
        
        report += "⚠️ **注意**: 请在确认项目正常运行后再删除备份目录\n"
        
        return report
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Android项目无用文件清理工具')
    parser.add_argument('report_file', help='扫描报告JSON文件路径')
    parser.add_argument('--no-backup', action='store_true', 
                       help='不创建备份（危险操作）')
    parser.add_argument('--resources-only', action='store_true',
                       help='只删除资源文件')
    parser.add_argument('--classes-only', action='store_true',
                       help='只删除类文件')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='自动确认删除（危险操作）')
    
    args = parser.parse_args()
    
    try:
        cleanup = UnusedFilesCleanup(args.report_file)
        report_data = cleanup.load_report()
        
        print("🧹 Android项目无用文件清理工具")
        print("=" * 50)
        print(f"📄 报告文件: {args.report_file}")
        print(f"📁 项目路径: {report_data['scan_info']['project_path']}")
        print(f"🗑️ 待删除资源文件: {report_data['scan_info']['total_unused_resources']} 个")
        print(f"🗑️ 待删除类文件: {report_data['scan_info']['total_unused_classes']} 个")
        print(f"💾 可节省空间: {report_data['scan_info']['total_size_formatted']}")
        
        # 确认操作
        if not args.auto_confirm:
            print("\n⚠️ 警告: 此操作将删除检测到的无用文件")
            if args.no_backup:
                print("⚠️ 您选择了不创建备份，删除的文件将无法恢复！")
            
            confirm = input("\n是否继续？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 操作已取消")
                return 0
        
        # 执行清理
        delete_resources = not args.classes_only
        delete_classes = not args.resources_only
        
        results = cleanup.cleanup(
            create_backup=not args.no_backup,
            delete_resources=delete_resources,
            delete_classes=delete_classes
        )
        
        # 生成清理报告
        cleanup_report = cleanup.generate_cleanup_report(results)
        report_file = Path(args.report_file).parent / f"cleanup_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(cleanup_report)
        
        print(f"\n✅ 清理完成!")
        print(f"📊 删除了 {results['total_deleted']} 个文件")
        print(f"💾 节省空间 {cleanup._format_size(results['saved_size'])}")
        print(f"📄 清理报告: {report_file}")
        
        if not args.no_backup and cleanup.backup_dir:
            print(f"📦 备份目录: {cleanup.backup_dir}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    import time
    exit(main())
